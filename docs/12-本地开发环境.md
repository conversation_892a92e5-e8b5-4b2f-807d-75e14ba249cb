# 本地开发环境配置指南

## Mac开发环境要求

### 系统要求
- **操作系统**: macOS 12.0+ (Monterey)
- **内存**: 8GB以上 (推荐16GB)
- **存储**: 20GB可用空间
- **网络**: 稳定的互联网连接

### 必需软件安装

#### 1. Homebrew包管理器
```bash
# 安装Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 验证安装
brew --version
```

#### 2. Node.js环境
```bash
# 安装Node.js 18.x LTS
brew install node@18

# 验证安装
node --version  # 应显示v18.x.x
npm --version   # 应显示9.x.x

# 全局安装必要工具
npm install -g pnpm pm2 @astro/cli
```

#### 3. MySQL数据库
```bash
# 安装MySQL
brew install mysql

# 启动MySQL服务
brew services start mysql

# 设置root密码（可选）
mysql_secure_installation

# 验证安装
mysql --version
```

#### 4. Git版本控制
```bash
# 安装Git（通常已预装）
brew install git

# 配置Git
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 验证配置
git config --list
```

#### 5. 开发工具
```bash
# 安装VS Code
brew install --cask visual-studio-code

# 安装Chrome（用于测试）
brew install --cask google-chrome

# 安装Postman（API测试）
brew install --cask postman
```

## 项目克隆和初始化

### 1. 克隆项目
```bash
# 创建工作目录
mkdir -p ~/Projects
cd ~/Projects

# 克隆项目（假设从Git仓库）
git clone <repository-url> pet-blog-system
cd pet-blog-system

# 或者创建新项目
mkdir pet-blog-system
cd pet-blog-system
```

### 2. 项目结构初始化
```bash
# 按照步骤1的要求创建目录结构
mkdir -p {backend,frontend,admin,docs,scripts,tests}

# 创建后端目录
mkdir -p backend/{src/{controllers/{admin,public},middleware,models,routes/{admin,public},services,utils,config,types},prisma,uploads,logs,tests}

# 创建前端目录
mkdir -p frontend/{src/{components/{common,layout,seo,ui},layouts,pages,styles,utils,types,config},templates/{en,de,ru},public/{images,icons},dist}

# 创建管理后台目录
mkdir -p admin/{src/{components,pages,utils,types,services},public,dist}
```

## 本地数据库配置

### 1. 创建本地数据库
```bash
# 连接到MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE pet_blog_local CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 创建开发用户
CREATE USER 'dev_user'@'localhost' IDENTIFIED BY 'dev_password';
GRANT ALL PRIVILEGES ON pet_blog_local.* TO 'dev_user'@'localhost';
FLUSH PRIVILEGES;

# 退出MySQL
EXIT;
```

### 2. 配置数据库连接
```bash
# 后端环境变量
cat > backend/.env << EOF
# 本地数据库配置
DATABASE_URL="mysql://dev_user:dev_password@localhost:3306/pet_blog_local"

# JWT配置
JWT_SECRET="local-development-secret-key-$(openssl rand -hex 16)"
JWT_EXPIRES_IN="24h"

# 翻译API配置
TRANSLATION_API_URL="https://ai.wanderintree.top"
TRANSLATION_API_KEY="sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d"
TRANSLATION_MODEL="gemini-2.5-pro"

# 服务器配置
PORT=3000
NODE_ENV="development"

# 文件上传配置
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=10485760

# 本地域名映射
DOMAIN_LANGUAGE_MAP='{"localhost:4321":"en","127.0.0.1:4321":"en","localhost:4322":"de","localhost:4323":"ru"}'

# CORS配置
ALLOWED_ORIGINS="http://localhost:4321,http://localhost:4322,http://localhost:4323,http://localhost:3001"
EOF
```

## 本地域名配置

### 1. 配置hosts文件
```bash
# 编辑hosts文件
sudo vim /etc/hosts

# 添加以下内容
127.0.0.1 petcare-local.com
127.0.0.1 haustier-local.de
127.0.0.1 domashnie-local.ru
127.0.0.1 admin-local.com
```

### 2. 配置本地SSL证书（可选）
```bash
# 安装mkcert
brew install mkcert

# 创建本地CA
mkcert -install

# 生成本地SSL证书
mkdir -p certs
cd certs
mkcert petcare-local.com haustier-local.de domashnie-local.ru admin-local.com localhost 127.0.0.1
```

## 后端开发环境

### 1. 安装依赖
```bash
cd backend

# 初始化package.json
npm init -y

# 安装生产依赖
npm install express cors helmet morgan bcryptjs jsonwebtoken multer sharp prisma @prisma/client express-rate-limit joi

# 安装开发依赖
npm install -D typescript @types/node @types/express @types/cors @types/bcryptjs @types/jsonwebtoken @types/multer ts-node nodemon prisma jest @types/jest ts-jest supertest @types/supertest
```

### 2. 配置TypeScript
```json
// backend/tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "tests"]
}
```

### 3. 配置开发脚本
```json
// backend/package.json scripts部分
{
  "scripts": {
    "dev": "nodemon src/app.ts",
    "build": "tsc",
    "start": "node dist/app.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "db:migrate": "prisma migrate dev",
    "db:generate": "prisma generate",
    "db:studio": "prisma studio",
    "db:seed": "ts-node prisma/seed.ts"
  }
}
```

### 4. 初始化Prisma
```bash
# 初始化Prisma
npx prisma init

# 配置schema.prisma（参考数据库设计文档）

# 生成迁移
npx prisma migrate dev --name init

# 生成客户端
npx prisma generate

# 打开Prisma Studio（可选）
npx prisma studio
```

## 前端开发环境

### 1. 初始化Astro项目
```bash
cd frontend

# 创建Astro项目
npm create astro@latest . -- --template minimal --typescript

# 安装额外依赖
npm install @astrojs/tailwind @astrojs/react @astrojs/sitemap tailwindcss @tailwindcss/typography

# 安装开发依赖
npm install -D @types/react @types/react-dom vitest @testing-library/react @testing-library/jest-dom jsdom
```

### 2. 配置Astro
```javascript
// frontend/astro.config.mjs
import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import react from '@astrojs/react';
import sitemap from '@astrojs/sitemap';

export default defineConfig({
  integrations: [
    tailwind(),
    react(),
    sitemap()
  ],
  server: {
    port: 4321,
    host: true
  },
  build: {
    assets: 'assets'
  }
});
```

### 3. 配置多语言构建脚本
```json
// frontend/package.json scripts部分
{
  "scripts": {
    "dev": "astro dev",
    "dev:en": "LANGUAGE=en astro dev --port 4321",
    "dev:de": "LANGUAGE=de astro dev --port 4322", 
    "dev:ru": "LANGUAGE=ru astro dev --port 4323",
    "build": "astro build",
    "build:en": "LANGUAGE=en astro build --outDir dist/en",
    "build:de": "LANGUAGE=de astro build --outDir dist/de",
    "build:ru": "LANGUAGE=ru astro build --outDir dist/ru",
    "preview": "astro preview",
    "test": "vitest",
    "test:ui": "vitest --ui"
  }
}
```

## 管理后台开发环境

### 1. 初始化React项目
```bash
cd admin

# 创建Vite React项目
npm create vite@latest . -- --template react-ts

# 安装依赖
npm install @tanstack/react-query axios react-router-dom @headlessui/react @heroicons/react

# 安装开发依赖
npm install -D tailwindcss postcss autoprefixer @types/node
```

### 2. 配置开发服务器
```typescript
// admin/vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3001,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true
      }
    }
  }
});
```

## 开发工作流程

### 1. 启动开发环境
```bash
# 在项目根目录创建启动脚本
cat > start-dev.sh << 'EOF'
#!/bin/bash

echo "启动宠物博客开发环境..."

# 启动后端
echo "启动后端服务..."
cd backend && npm run dev &
BACKEND_PID=$!

# 等待后端启动
sleep 5

# 启动前端（英语版本）
echo "启动前端服务..."
cd ../frontend && npm run dev:en &
FRONTEND_PID=$!

# 启动管理后台
echo "启动管理后台..."
cd ../admin && npm run dev &
ADMIN_PID=$!

echo "开发环境启动完成！"
echo "前端地址: http://localhost:4321"
echo "管理后台: http://localhost:3001"
echo "API地址: http://localhost:3000"

# 等待用户中断
wait

# 清理进程
kill $BACKEND_PID $FRONTEND_PID $ADMIN_PID 2>/dev/null
EOF

chmod +x start-dev.sh
```

### 2. 数据库管理脚本
```bash
# 创建数据库管理脚本
cat > db-reset.sh << 'EOF'
#!/bin/bash

echo "重置本地数据库..."

cd backend

# 重置数据库
npx prisma migrate reset --force

# 运行种子数据
npm run db:seed

echo "数据库重置完成！"
EOF

chmod +x db-reset.sh
```

### 3. 测试脚本
```bash
# 创建测试脚本
cat > run-tests.sh << 'EOF'
#!/bin/bash

echo "运行所有测试..."

# 后端测试
echo "运行后端测试..."
cd backend && npm test

# 前端测试
echo "运行前端测试..."
cd ../frontend && npm test

# 管理后台测试
echo "运行管理后台测试..."
cd ../admin && npm test

echo "所有测试完成！"
EOF

chmod +x run-tests.sh
```

## VS Code配置

### 1. 推荐扩展
```json
// .vscode/extensions.json
{
  "recommendations": [
    "astro-build.astro-vscode",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-json",
    "prisma.prisma",
    "ms-vscode.vscode-jest"
  ]
}
```

### 2. 工作区配置
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "files.associations": {
    "*.astro": "astro"
  },
  "emmet.includeLanguages": {
    "astro": "html"
  },
  "tailwindCSS.includeLanguages": {
    "astro": "html"
  }
}
```

### 3. 调试配置
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Backend",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/backend/src/app.ts",
      "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"],
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    }
  ]
}
```

## 常见问题解决

### 1. 端口冲突
```bash
# 查看端口占用
lsof -i :3000
lsof -i :4321

# 杀死占用进程
kill -9 <PID>
```

### 2. 数据库连接问题
```bash
# 检查MySQL服务状态
brew services list | grep mysql

# 重启MySQL服务
brew services restart mysql

# 测试数据库连接
mysql -u dev_user -p pet_blog_local
```

### 3. Node.js版本问题
```bash
# 使用nvm管理Node.js版本
brew install nvm

# 安装并使用Node.js 18
nvm install 18
nvm use 18
nvm alias default 18
```

这个本地开发环境配置确保了在Mac上能够顺利进行项目开发，包含了所有必要的工具和配置。
