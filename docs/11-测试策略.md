# 测试策略文档

## 测试总体策略

### 测试金字塔
```
    E2E Tests (10%)
   ┌─────────────────┐
  │  集成测试 (20%)   │
 ┌─────────────────────┐
│   单元测试 (70%)     │
└─────────────────────┘
```

### 测试覆盖率目标
- **单元测试覆盖率**: ≥ 80%
- **集成测试覆盖率**: ≥ 60%
- **E2E测试覆盖率**: ≥ 90% (关键用户路径)

## 后端测试策略

### 单元测试

#### 测试框架配置
```json
// backend/package.json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false"
  },
  "devDependencies": {
    "jest": "^29.0.0",
    "@types/jest": "^29.0.0",
    "ts-jest": "^29.0.0",
    "supertest": "^6.3.0",
    "@types/supertest": "^2.0.12"
  }
}
```

#### Jest配置
```javascript
// backend/jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/types/**',
    '!src/config/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts']
};
```

#### 数据库测试配置
```typescript
// tests/setup.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_TEST_URL || 'mysql://test:test@localhost:3306/test_db'
    }
  }
});

beforeAll(async () => {
  // 清理测试数据库
  await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 0`;
  
  const tables = await prisma.$queryRaw<Array<{ table_name: string }>>`
    SELECT table_name FROM information_schema.tables 
    WHERE table_schema = DATABASE()
  `;
  
  for (const table of tables) {
    await prisma.$executeRawUnsafe(`TRUNCATE TABLE ${table.table_name}`);
  }
  
  await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 1`;
});

afterAll(async () => {
  await prisma.$disconnect();
});

export { prisma };
```

#### 服务层测试示例
```typescript
// tests/services/articleService.test.ts
import { ArticleService } from '../../src/services/articleService';
import { prisma } from '../setup';

describe('ArticleService', () => {
  let articleService: ArticleService;

  beforeEach(() => {
    articleService = new ArticleService();
  });

  describe('getArticles', () => {
    it('应该返回已发布的文章列表', async () => {
      // 准备测试数据
      const category = await prisma.category.create({
        data: { slug: 'test-category' }
      });

      const article = await prisma.article.create({
        data: {
          originalTitle: 'Test Article',
          originalContent: 'Test content',
          slug: 'test-article',
          categoryId: category.id,
          status: 'PUBLISHED'
        }
      });

      await prisma.articleTranslation.create({
        data: {
          articleId: article.id,
          languageCode: 'en',
          title: 'Test Article',
          slug: 'test-article',
          content: 'Test content',
          status: 'PUBLISHED'
        }
      });

      // 执行测试
      const result = await articleService.getArticles({
        language: 'en',
        page: 1,
        limit: 10
      });

      // 验证结果
      expect(result.articles).toHaveLength(1);
      expect(result.articles[0].title).toBe('Test Article');
      expect(result.pagination.totalItems).toBe(1);
    });

    it('应该支持分类筛选', async () => {
      // 测试分类筛选功能
    });

    it('应该支持分页', async () => {
      // 测试分页功能
    });
  });

  describe('getArticleBySlug', () => {
    it('应该返回指定slug的文章详情', async () => {
      // 测试文章详情获取
    });

    it('应该在文章不存在时抛出错误', async () => {
      await expect(
        articleService.getArticleBySlug('non-existent', 'en')
      ).rejects.toThrow('Article not found');
    });
  });
});
```

#### API控制器测试示例
```typescript
// tests/controllers/articleController.test.ts
import request from 'supertest';
import app from '../../src/app';
import { prisma } from '../setup';

describe('Article API', () => {
  describe('GET /api/articles', () => {
    it('应该返回文章列表', async () => {
      const response = await request(app)
        .get('/api/articles')
        .set('Host', 'petcare-usa.com')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.articles).toBeDefined();
      expect(response.body.data.pagination).toBeDefined();
    });

    it('应该支持分页参数', async () => {
      const response = await request(app)
        .get('/api/articles?page=2&limit=5')
        .set('Host', 'petcare-usa.com')
        .expect(200);

      expect(response.body.data.pagination.currentPage).toBe(2);
    });
  });

  describe('GET /api/articles/:slug', () => {
    it('应该返回文章详情', async () => {
      // 创建测试文章
      const article = await createTestArticle();

      const response = await request(app)
        .get(`/api/articles/${article.slug}`)
        .set('Host', 'petcare-usa.com')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.title).toBe(article.title);
    });

    it('应该在文章不存在时返回404', async () => {
      await request(app)
        .get('/api/articles/non-existent')
        .set('Host', 'petcare-usa.com')
        .expect(404);
    });
  });
});
```

### 集成测试

#### API集成测试
```typescript
// tests/integration/api.test.ts
describe('API Integration Tests', () => {
  describe('文章发布流程', () => {
    it('应该完成完整的文章发布流程', async () => {
      // 1. 管理员登录
      const loginResponse = await request(app)
        .post('/api/admin/auth/login')
        .send({
          username: 'admin',
          password: 'password'
        });

      const token = loginResponse.body.data.token;

      // 2. 创建文章
      const createResponse = await request(app)
        .post('/api/admin/articles')
        .set('Authorization', `Bearer ${token}`)
        .send({
          title: 'Integration Test Article',
          content: 'Test content',
          categoryId: 1
        });

      const articleId = createResponse.body.data.id;

      // 3. 发布文章
      await request(app)
        .patch(`/api/admin/articles/${articleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({ status: 'PUBLISHED' });

      // 4. 验证前端可以访问
      const publicResponse = await request(app)
        .get(`/api/articles/${createResponse.body.data.slug}`)
        .set('Host', 'petcare-usa.com');

      expect(publicResponse.status).toBe(200);
      expect(publicResponse.body.data.title).toBe('Integration Test Article');
    });
  });

  describe('翻译流程', () => {
    it('应该完成文章翻译流程', async () => {
      // 测试AI翻译集成
    });
  });
});
```

### 性能测试

#### 负载测试配置
```javascript
// tests/performance/load.test.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // 2分钟内增加到100用户
    { duration: '5m', target: 100 }, // 保持100用户5分钟
    { duration: '2m', target: 200 }, // 2分钟内增加到200用户
    { duration: '5m', target: 200 }, // 保持200用户5分钟
    { duration: '2m', target: 0 },   // 2分钟内减少到0用户
  ],
  thresholds: {
    http_req_duration: ['p(99)<1500'], // 99%的请求在1.5秒内完成
    http_req_failed: ['rate<0.1'],     // 错误率小于10%
  },
};

export default function () {
  // 测试首页
  let response = http.get('https://petcare-usa.com');
  check(response, {
    '首页状态码为200': (r) => r.status === 200,
    '首页响应时间<2s': (r) => r.timings.duration < 2000,
  });

  sleep(1);

  // 测试API
  response = http.get('https://petcare-usa.com/api/articles');
  check(response, {
    'API状态码为200': (r) => r.status === 200,
    'API响应时间<1s': (r) => r.timings.duration < 1000,
  });

  sleep(1);
}
```

## 前端测试策略

### 组件测试

#### 测试框架配置
```json
// frontend/package.json
{
  "scripts": {
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage"
  },
  "devDependencies": {
    "vitest": "^0.34.0",
    "@testing-library/react": "^13.4.0",
    "@testing-library/jest-dom": "^5.16.5",
    "@testing-library/user-event": "^14.4.3",
    "jsdom": "^22.1.0"
  }
}
```

#### Vitest配置
```typescript
// frontend/vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
      ],
    },
  },
});
```

#### 组件测试示例
```typescript
// frontend/src/components/__tests__/ArticleCard.test.tsx
import { render, screen } from '@testing-library/react';
import { ArticleCard } from '../ArticleCard';

const mockArticle = {
  id: 1,
  title: 'Test Article',
  excerpt: 'Test excerpt',
  slug: 'test-article',
  featuredImage: '/test-image.jpg',
  publishDate: '2023-01-01',
  category: { name: 'Test Category', slug: 'test-category' },
  readTime: 5,
  viewCount: 100
};

describe('ArticleCard', () => {
  it('应该渲染文章信息', () => {
    render(<ArticleCard article={mockArticle} />);

    expect(screen.getByText('Test Article')).toBeInTheDocument();
    expect(screen.getByText('Test excerpt')).toBeInTheDocument();
    expect(screen.getByText('Test Category')).toBeInTheDocument();
    expect(screen.getByText('5 min read')).toBeInTheDocument();
  });

  it('应该渲染特色图片', () => {
    render(<ArticleCard article={mockArticle} />);

    const image = screen.getByAltText('Test Article');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', '/test-image.jpg');
  });

  it('应该有正确的链接', () => {
    render(<ArticleCard article={mockArticle} />);

    const link = screen.getByRole('link');
    expect(link).toHaveAttribute('href', '/articles/test-article');
  });
});
```

### E2E测试

#### Playwright配置
```typescript
// tests/e2e/playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'https://petcare-usa.com',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
  ],
});
```

#### E2E测试示例
```typescript
// tests/e2e/user-journey.spec.ts
import { test, expect } from '@playwright/test';

test.describe('用户浏览文章流程', () => {
  test('用户可以浏览首页并阅读文章', async ({ page }) => {
    // 访问首页
    await page.goto('/');
    
    // 验证页面标题
    await expect(page).toHaveTitle(/Pet Care/);
    
    // 验证文章列表存在
    await expect(page.locator('[data-testid="article-grid"]')).toBeVisible();
    
    // 点击第一篇文章
    const firstArticle = page.locator('[data-testid="article-card"]').first();
    await firstArticle.click();
    
    // 验证文章详情页
    await expect(page.locator('[data-testid="article-content"]')).toBeVisible();
    await expect(page.locator('h1')).toBeVisible();
    
    // 验证评论区域
    await expect(page.locator('[data-testid="comment-section"]')).toBeVisible();
  });

  test('用户可以搜索文章', async ({ page }) => {
    await page.goto('/');
    
    // 使用搜索功能
    await page.fill('[data-testid="search-input"]', 'cat care');
    await page.press('[data-testid="search-input"]', 'Enter');
    
    // 验证搜索结果
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible();
    await expect(page.locator('[data-testid="search-result-item"]')).toHaveCount.greaterThan(0);
  });

  test('用户可以提交评论', async ({ page }) => {
    await page.goto('/articles/test-article');
    
    // 填写评论表单
    await page.fill('[data-testid="comment-name"]', 'Test User');
    await page.fill('[data-testid="comment-email"]', '<EMAIL>');
    await page.fill('[data-testid="comment-content"]', 'This is a test comment');
    
    // 提交评论
    await page.click('[data-testid="comment-submit"]');
    
    // 验证成功消息
    await expect(page.locator('[data-testid="comment-success"]')).toBeVisible();
  });
});

test.describe('管理后台功能', () => {
  test('管理员可以登录并管理文章', async ({ page }) => {
    // 访问管理后台
    await page.goto('https://admin.petcare-usa.com');
    
    // 登录
    await page.fill('[data-testid="login-username"]', 'admin');
    await page.fill('[data-testid="login-password"]', 'password');
    await page.click('[data-testid="login-submit"]');
    
    // 验证登录成功
    await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
    
    // 进入文章管理
    await page.click('[data-testid="nav-articles"]');
    await expect(page.locator('[data-testid="article-list"]')).toBeVisible();
    
    // 创建新文章
    await page.click('[data-testid="create-article"]');
    await page.fill('[data-testid="article-title"]', 'E2E Test Article');
    await page.fill('[data-testid="article-content"]', 'This is test content');
    await page.click('[data-testid="article-save"]');
    
    // 验证文章创建成功
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  });
});
```

## 测试数据管理

### 测试数据工厂
```typescript
// tests/factories/articleFactory.ts
import { faker } from '@faker-js/faker';
import { prisma } from '../setup';

export class ArticleFactory {
  static async create(overrides: Partial<any> = {}) {
    const category = await prisma.category.create({
      data: {
        slug: faker.lorem.slug(),
        ...overrides.category
      }
    });

    const article = await prisma.article.create({
      data: {
        originalTitle: faker.lorem.sentence(),
        originalContent: faker.lorem.paragraphs(3),
        slug: faker.lorem.slug(),
        categoryId: category.id,
        status: 'PUBLISHED',
        ...overrides
      }
    });

    await prisma.articleTranslation.create({
      data: {
        articleId: article.id,
        languageCode: 'en',
        title: article.originalTitle,
        slug: article.slug,
        content: article.originalContent,
        status: 'PUBLISHED'
      }
    });

    return article;
  }

  static async createMany(count: number, overrides: Partial<any> = {}) {
    const articles = [];
    for (let i = 0; i < count; i++) {
      articles.push(await this.create(overrides));
    }
    return articles;
  }
}
```

## 持续集成测试

### GitHub Actions配置
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:9.0.1
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: test_db
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json
    
    - name: Install dependencies
      run: |
        cd backend
        npm ci
    
    - name: Run tests
      run: |
        cd backend
        npm run test:ci
      env:
        DATABASE_TEST_URL: mysql://root:root@localhost:3306/test_db
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: backend/coverage/lcov.info

  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Run tests
      run: |
        cd frontend
        npm run test:coverage

  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Install Playwright
      run: npx playwright install
    
    - name: Run E2E tests
      run: npx playwright test
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: playwright-report
        path: playwright-report/
```

这个测试策略确保了系统的质量和稳定性，覆盖了从单元测试到E2E测试的完整测试金字塔。
