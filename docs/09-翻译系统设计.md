# AI翻译系统设计文档

## 翻译系统架构

### 系统概述
本翻译系统基于OpenAI兼容API（Gemini 2.5 Pro），实现文章内容的自动翻译和人工校对流程。

### 翻译流程设计

```mermaid
graph TD
    A[原文文章] --> B[提取翻译内容]
    B --> C[调用AI翻译API]
    C --> D[翻译结果处理]
    D --> E[保存翻译草稿]
    E --> F[人工校对]
    F --> G[发布翻译版本]
    
    H[翻译状态管理] --> E
    H --> F
    H --> G
```

### 翻译状态管理

#### 翻译状态枚举
```typescript
enum TranslationStatus {
  PENDING = 'PENDING',        // 待翻译
  TRANSLATING = 'TRANSLATING', // 翻译中
  TRANSLATED = 'TRANSLATED',   // 已翻译
  REVIEWING = 'REVIEWING',     // 校对中
  REVIEWED = 'REVIEWED',       // 已校对
  PUBLISHED = 'PUBLISHED'      // 已发布
}
```

#### 状态流转规则
- PENDING → TRANSLATING：开始翻译
- TRANSLATING → TRANSLATED：翻译完成
- TRANSLATED → REVIEWING：开始校对
- REVIEWING → REVIEWED：校对完成
- REVIEWED → PUBLISHED：发布翻译

### API集成配置

#### 环境变量配置
```env
# 翻译API配置
TRANSLATION_API_URL="https://ai.wanderintree.top"
TRANSLATION_API_KEY="sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d"
TRANSLATION_MODEL="gemini-2.5-pro"
TRANSLATION_MAX_TOKENS=4000
TRANSLATION_TEMPERATURE=0.3
```

#### API请求格式
```typescript
interface TranslationRequest {
  model: string;
  messages: Array<{
    role: 'system' | 'user';
    content: string;
  }>;
  max_tokens: number;
  temperature: number;
}
```

### 翻译服务实现

#### TranslationService类设计
```typescript
export class TranslationService {
  private apiUrl: string;
  private apiKey: string;
  private model: string;

  constructor() {
    this.apiUrl = process.env.TRANSLATION_API_URL!;
    this.apiKey = process.env.TRANSLATION_API_KEY!;
    this.model = process.env.TRANSLATION_MODEL!;
  }

  // 翻译文章标题
  async translateTitle(title: string, targetLanguage: string): Promise<string>

  // 翻译文章内容
  async translateContent(content: string, targetLanguage: string): Promise<string>

  // 翻译文章摘要
  async translateExcerpt(excerpt: string, targetLanguage: string): Promise<string>

  // 翻译SEO元数据
  async translateSEOData(seoData: SEOData, targetLanguage: string): Promise<SEOData>

  // 批量翻译
  async batchTranslate(items: TranslationItem[], targetLanguage: string): Promise<TranslationResult[]>
}
```

### 翻译提示词设计

#### 系统提示词模板
```typescript
const SYSTEM_PROMPTS = {
  title: {
    en: "You are a professional pet care content translator. Translate the following pet blog title to English. Keep it SEO-friendly, engaging, and under 60 characters. Focus on cats and dogs content.",
    de: "Sie sind ein professioneller Übersetzer für Haustierpflege-Inhalte. Übersetzen Sie den folgenden Haustierblog-Titel ins Deutsche. Halten Sie ihn SEO-freundlich, ansprechend und unter 60 Zeichen. Konzentrieren Sie sich auf Inhalte über Katzen und Hunde.",
    ru: "Вы профессиональный переводчик контента по уходу за домашними животными. Переведите следующий заголовок блога о домашних животных на русский язык. Сделайте его SEO-дружественным, привлекательным и менее 60 символов. Сосредоточьтесь на контенте о кошках и собаках."
  },
  content: {
    en: "You are a professional pet care content translator. Translate the following pet blog article to English. Maintain the HTML structure, keep all formatting, and ensure the content is natural, engaging, and informative for English-speaking pet owners. Focus on cats and dogs care.",
    de: "Sie sind ein professioneller Übersetzer für Haustierpflege-Inhalte. Übersetzen Sie den folgenden Haustierblog-Artikel ins Deutsche. Behalten Sie die HTML-Struktur bei, bewahren Sie alle Formatierungen und stellen Sie sicher, dass der Inhalt natürlich, ansprechend und informativ für deutschsprachige Haustierbesitzer ist.",
    ru: "Вы профессиональный переводчик контента по уходу за домашними животными. Переведите следующую статью блога о домашних животных на русский язык. Сохраните HTML-структуру, все форматирование и убедитесь, что контент естественный, привлекательный и информативный для русскоязычных владельцев домашних животных."
  }
};
```

### 翻译质量控制

#### 内容验证规则
```typescript
interface TranslationValidation {
  // 长度验证
  validateLength(original: string, translated: string): boolean;
  
  // HTML标签验证
  validateHTMLTags(original: string, translated: string): boolean;
  
  // 关键词保留验证
  validateKeywords(original: string, translated: string, keywords: string[]): boolean;
  
  // 语言检测
  detectLanguage(text: string): string;
}
```

#### 质量评分系统
```typescript
interface QualityScore {
  accuracy: number;      // 准确性 (0-100)
  fluency: number;       // 流畅性 (0-100)
  completeness: number;  // 完整性 (0-100)
  overall: number;       // 总体评分 (0-100)
}
```

### 翻译缓存策略

#### 缓存设计
```typescript
interface TranslationCache {
  key: string;           // 内容hash
  sourceLanguage: string;
  targetLanguage: string;
  originalText: string;
  translatedText: string;
  quality: QualityScore;
  createdAt: Date;
  expiresAt: Date;
}
```

#### 缓存策略
- 相同内容的翻译结果缓存30天
- 高质量翻译结果缓存更长时间
- 支持缓存失效和更新机制

### 翻译任务队列

#### 队列设计
```typescript
interface TranslationJob {
  id: string;
  articleId: number;
  targetLanguage: string;
  priority: 'low' | 'normal' | 'high';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
}
```

#### 队列处理策略
- 支持优先级队列
- 失败重试机制
- 并发控制
- 进度追踪

### 翻译API实现

#### 翻译控制器
```typescript
// POST /api/admin/translate/article/:id
export const translateArticle = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { targetLanguages } = req.body;

  try {
    // 获取原文文章
    const article = await articleService.getById(id);
    
    // 创建翻译任务
    const jobs = await translationService.createTranslationJobs(
      article, 
      targetLanguages
    );

    res.json({
      success: true,
      data: { jobs }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: { message: error.message }
    });
  }
};
```

#### 翻译状态查询
```typescript
// GET /api/admin/translate/status/:jobId
export const getTranslationStatus = async (req: Request, res: Response) => {
  const { jobId } = req.params;

  try {
    const job = await translationService.getJobStatus(jobId);
    
    res.json({
      success: true,
      data: { job }
    });
  } catch (error) {
    res.status(404).json({
      success: false,
      error: { message: 'Translation job not found' }
    });
  }
};
```

### 翻译工作流程

#### 自动翻译流程
1. **文章发布触发**：原文文章发布后自动创建翻译任务
2. **内容提取**：提取标题、内容、摘要、SEO数据
3. **分段翻译**：将长内容分段处理，避免token限制
4. **结果合并**：合并分段翻译结果
5. **质量检查**：验证翻译质量和完整性
6. **保存草稿**：保存为翻译草稿状态

#### 人工校对流程
1. **校对分配**：将翻译任务分配给校对人员
2. **在线编辑**：提供在线编辑界面
3. **对比查看**：原文和译文对比显示
4. **修改追踪**：记录所有修改历史
5. **质量评分**：校对人员给出质量评分
6. **发布审核**：最终发布前的审核确认

### 翻译性能优化

#### 并发控制
```typescript
class TranslationQueue {
  private concurrency = 3; // 同时处理3个翻译任务
  private queue: TranslationJob[] = [];
  private processing: Set<string> = new Set();

  async processQueue() {
    while (this.queue.length > 0 && this.processing.size < this.concurrency) {
      const job = this.queue.shift();
      if (job) {
        this.processing.add(job.id);
        this.processJob(job).finally(() => {
          this.processing.delete(job.id);
        });
      }
    }
  }
}
```

#### 错误处理和重试
```typescript
class TranslationRetryHandler {
  private maxRetries = 3;
  private retryDelay = 1000; // 1秒

  async executeWithRetry<T>(
    operation: () => Promise<T>,
    retries = this.maxRetries
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      if (retries > 0) {
        await this.delay(this.retryDelay);
        return this.executeWithRetry(operation, retries - 1);
      }
      throw error;
    }
  }
}
```

### 翻译监控和统计

#### 翻译统计指标
- 翻译任务数量
- 翻译成功率
- 平均翻译时间
- 翻译质量评分
- API调用统计

#### 监控告警
- API调用失败告警
- 翻译质量低于阈值告警
- 队列积压告警
- 系统异常告警

这个翻译系统设计确保了高质量的多语言内容生产，支持自动化翻译和人工校对的完整工作流程。
