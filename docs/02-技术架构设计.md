# 技术架构设计文档

## 系统整体架构

### 架构概览
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   英语域名      │    │   德语域名      │    │   俄语域名      │
│ petcare-usa.com │    │haustier-de.com  │    │ pets-ru.com     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Nginx反向代理  │
                    │   (宝塔面板)     │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Node.js API   │
                    │   (Express.js)  │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL 9.0.1   │
                    │  (远程数据库)    │
                    └─────────────────┘
```

### 技术栈详细说明

#### 前端技术栈
- **Astro 4.x**: 静态站点生成器，SEO友好
- **Tailwind CSS 3.x**: 原子化CSS框架
- **TypeScript**: 类型安全的JavaScript
- **React**: 用于交互组件（评论、搜索）
- **Vite**: 构建工具（Astro内置）

#### 后端技术栈
- **Node.js 18+**: JavaScript运行时
- **Express.js 4.x**: Web应用框架
- **Prisma 5.x**: 现代化ORM
- **TypeScript**: 类型安全
- **JWT**: 身份认证
- **Multer**: 文件上传处理
- **Sharp**: 图片处理

#### 数据库设计
- **MySQL 9.0.1**: 主数据库
- **连接池**: 优化数据库连接
- **索引优化**: 查询性能优化
- **备份策略**: 定期数据备份

## 多语言架构设计

### 域名路由策略
```javascript
// 域名映射配置
const DOMAIN_LANGUAGE_MAP = {
  'petcare-usa.com': 'en',
  'haustier-deutschland.de': 'de', 
  'domashnie-zhivotnye.ru': 'ru'
};

// 中间件识别语言
app.use((req, res, next) => {
  const host = req.get('host');
  const language = DOMAIN_LANGUAGE_MAP[host] || 'en';
  req.language = language;
  next();
});
```

### 模板结构设计
```
frontend/
├── templates/
│   ├── en/                 # 英语模板
│   │   ├── pages/
│   │   ├── components/
│   │   ├── layouts/
│   │   └── styles/
│   ├── de/                 # 德语模板
│   │   ├── pages/
│   │   ├── components/
│   │   ├── layouts/
│   │   └── styles/
│   └── ru/                 # 俄语模板
│       ├── pages/
│       ├── components/
│       ├── layouts/
│       └── styles/
└── shared/                 # 共享组件
    ├── utils/
    ├── types/
    └── api/
```

### 构建部署策略
```javascript
// 构建脚本
const languages = ['en', 'de', 'ru'];

languages.forEach(lang => {
  // 为每种语言构建独立的静态站点
  buildStaticSite(lang, {
    template: `templates/${lang}`,
    output: `dist/${lang}`,
    domain: DOMAIN_LANGUAGE_MAP[lang]
  });
});
```

## 数据库架构设计

### 核心表结构

#### 1. 语言配置表 (languages)
```sql
CREATE TABLE languages (
  id INT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(5) NOT NULL UNIQUE,     -- 'en', 'de', 'ru'
  name VARCHAR(50) NOT NULL,           -- 'English', 'Deutsch', 'Русский'
  domain VARCHAR(100) NOT NULL UNIQUE, -- 域名
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. 分类表 (categories)
```sql
CREATE TABLE categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  parent_id INT NULL,                  -- 父分类ID，支持二级分类
  slug VARCHAR(100) NOT NULL,          -- URL slug
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
  INDEX idx_parent_id (parent_id),
  INDEX idx_slug (slug)
);
```

#### 3. 分类翻译表 (category_translations)
```sql
CREATE TABLE category_translations (
  id INT PRIMARY KEY AUTO_INCREMENT,
  category_id INT NOT NULL,
  language_code VARCHAR(5) NOT NULL,
  name VARCHAR(100) NOT NULL,          -- 分类名称
  description TEXT,                    -- 分类描述
  meta_title VARCHAR(200),             -- SEO标题
  meta_description VARCHAR(300),       -- SEO描述
  
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
  FOREIGN KEY (language_code) REFERENCES languages(code) ON DELETE CASCADE,
  UNIQUE KEY unique_category_language (category_id, language_code),
  INDEX idx_language_code (language_code)
);
```

#### 4. 文章表 (articles)
```sql
CREATE TABLE articles (
  id INT PRIMARY KEY AUTO_INCREMENT,
  slug VARCHAR(200) NOT NULL UNIQUE,   -- 原始slug（中文）
  category_id INT NOT NULL,
  featured_image VARCHAR(500),         -- 特色图片路径
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  publish_date TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
  INDEX idx_status (status),
  INDEX idx_publish_date (publish_date),
  INDEX idx_category_id (category_id)
);
```

#### 5. 文章翻译表 (article_translations)
```sql
CREATE TABLE article_translations (
  id INT PRIMARY KEY AUTO_INCREMENT,
  article_id INT NOT NULL,
  language_code VARCHAR(5) NOT NULL,
  title VARCHAR(300) NOT NULL,         -- 文章标题
  slug VARCHAR(200) NOT NULL,          -- 本地化slug
  content LONGTEXT NOT NULL,           -- 文章内容
  excerpt TEXT,                        -- 文章摘要
  meta_title VARCHAR(200),             -- SEO标题
  meta_description VARCHAR(300),       -- SEO描述
  meta_keywords VARCHAR(500),          -- SEO关键词
  status ENUM('draft', 'published') DEFAULT 'draft',
  
  FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
  FOREIGN KEY (language_code) REFERENCES languages(code) ON DELETE CASCADE,
  UNIQUE KEY unique_article_language (article_id, language_code),
  UNIQUE KEY unique_language_slug (language_code, slug),
  INDEX idx_language_code (language_code),
  INDEX idx_status (status),
  FULLTEXT idx_content (title, content, excerpt)
);
```

#### 6. 评论表 (comments)
```sql
CREATE TABLE comments (
  id INT PRIMARY KEY AUTO_INCREMENT,
  article_id INT NOT NULL,
  language_code VARCHAR(5) NOT NULL,
  parent_id INT NULL,                  -- 父评论ID，支持嵌套
  author_name VARCHAR(100) NOT NULL,
  author_email VARCHAR(200) NOT NULL,
  content TEXT NOT NULL,
  status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
  ip_address VARCHAR(45),              -- 支持IPv6
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
  FOREIGN KEY (language_code) REFERENCES languages(code) ON DELETE CASCADE,
  FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
  INDEX idx_article_language (article_id, language_code),
  INDEX idx_status (status),
  INDEX idx_parent_id (parent_id)
);
```

#### 7. 站点配置表 (site_settings)
```sql
CREATE TABLE site_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  language_code VARCHAR(5) NOT NULL,
  setting_key VARCHAR(100) NOT NULL,
  setting_value TEXT,
  setting_type ENUM('text', 'textarea', 'boolean', 'json') DEFAULT 'text',
  
  FOREIGN KEY (language_code) REFERENCES languages(code) ON DELETE CASCADE,
  UNIQUE KEY unique_language_setting (language_code, setting_key),
  INDEX idx_language_code (language_code)
);
```

#### 8. 管理员表 (admins)
```sql
CREATE TABLE admins (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(200) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  last_login TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_username (username),
  INDEX idx_email (email)
);
```

### 索引优化策略

#### 查询优化索引
```sql
-- 文章列表查询优化
CREATE INDEX idx_article_list ON article_translations 
(language_code, status, article_id);

-- 分类文章查询优化  
CREATE INDEX idx_category_articles ON articles 
(category_id, status, publish_date DESC);

-- 评论查询优化
CREATE INDEX idx_comment_tree ON comments 
(article_id, language_code, parent_id, status);

-- 搜索优化
CREATE FULLTEXT INDEX idx_search ON article_translations 
(title, content, excerpt);
```

## API架构设计

### RESTful API设计

#### 公共API（前端调用）
```
GET    /api/articles                    # 获取文章列表
GET    /api/articles/:slug              # 获取文章详情
GET    /api/categories                  # 获取分类列表
GET    /api/categories/:slug/articles   # 获取分类下的文章
POST   /api/comments                    # 提交评论
GET    /api/search                      # 搜索文章
GET    /api/sitemap.xml                 # 站点地图
```

#### 管理API（后台调用）
```
POST   /api/admin/login                 # 管理员登录
GET    /api/admin/articles              # 获取文章管理列表
POST   /api/admin/articles              # 创建文章
PUT    /api/admin/articles/:id          # 更新文章
DELETE /api/admin/articles/:id          # 删除文章
POST   /api/admin/articles/:id/translate # 翻译文章
GET    /api/admin/comments              # 获取评论管理列表
PUT    /api/admin/comments/:id          # 审核评论
POST   /api/admin/upload                # 上传图片
GET    /api/admin/settings              # 获取站点设置
PUT    /api/admin/settings              # 更新站点设置
```

### 中间件设计

#### 1. 语言识别中间件
```javascript
const languageMiddleware = (req, res, next) => {
  const host = req.get('host');
  const language = DOMAIN_LANGUAGE_MAP[host] || 'en';
  req.language = language;
  next();
};
```

#### 2. 认证中间件
```javascript
const authMiddleware = (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.admin = decoded;
    next();
  } catch (error) {
    return res.status(401).json({ error: 'Invalid token' });
  }
};
```

#### 3. 错误处理中间件
```javascript
const errorHandler = (err, req, res, next) => {
  console.error(err.stack);
  
  if (err.name === 'ValidationError') {
    return res.status(400).json({ error: err.message });
  }
  
  res.status(500).json({ error: 'Internal server error' });
};
```

## 文件存储架构

### 图片存储策略
```
uploads/
├── images/
│   ├── articles/           # 文章图片
│   │   ├── 2024/
│   │   │   ├── 01/
│   │   │   └── 02/
│   │   └── thumbnails/     # 缩略图
│   └── temp/               # 临时上传文件
└── static/                 # 静态资源
    ├── css/
    ├── js/
    └── fonts/
```

### 图片处理流程
```javascript
// 图片上传处理
const processImage = async (file) => {
  const filename = generateUniqueFilename(file.originalname);
  const outputPath = `uploads/images/articles/${getCurrentYearMonth()}/${filename}`;
  
  // 生成多种尺寸
  await sharp(file.buffer)
    .resize(1200, 800, { fit: 'inside', withoutEnlargement: true })
    .webp({ quality: 85 })
    .toFile(outputPath);
    
  // 生成缩略图
  await sharp(file.buffer)
    .resize(400, 300, { fit: 'cover' })
    .webp({ quality: 80 })
    .toFile(`uploads/images/thumbnails/${filename}`);
    
  return outputPath;
};
```

## 缓存架构设计

### 缓存策略
```javascript
// Redis缓存配置（可选）
const cacheConfig = {
  // 文章缓存 - 1小时
  articles: { ttl: 3600 },
  // 分类缓存 - 24小时  
  categories: { ttl: 86400 },
  // 站点设置缓存 - 12小时
  settings: { ttl: 43200 }
};

// 内存缓存（开发环境）
const memoryCache = new Map();
```

### CDN集成准备
```javascript
// CDN配置接口
const cdnConfig = {
  enabled: process.env.CDN_ENABLED === 'true',
  baseUrl: process.env.CDN_BASE_URL,
  imageTransform: true
};
```

这个技术架构设计为整个系统提供了坚实的基础，支持多语言、高性能、易扩展的需求。
