# 部署配置指南

## 服务器环境要求

### 硬件配置
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 50GB以上SSD
- **带宽**: 10Mbps以上

### 软件环境
- **操作系统**: Debian 12 / Ubuntu 20.04+
- **宝塔面板**: 7.7.0+
- **Node.js**: 18.x LTS
- **MySQL**: 9.0.1
- **Nginx**: 1.20+
- **PM2**: 5.x

## 宝塔面板配置

### 基础环境安装
```bash
# 安装宝塔面板
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh

# 安装软件环境
# 在宝塔面板中安装：
# - Nginx 1.20+
# - MySQL 9.0.1
# - Node.js 18.x
# - PM2管理器
```

### Node.js环境配置
```bash
# 通过宝塔面板Node.js版本管理器安装
# 选择Node.js 18.x LTS版本
# 全局安装PM2
npm install -g pm2
```

## 数据库配置

### MySQL配置优化
```sql
-- 在宝塔面板MySQL配置中添加
[mysqld]
# 字符集配置
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# 性能优化
innodb_buffer_pool_size=1G
innodb_log_file_size=256M
max_connections=500
query_cache_size=128M

# 时区设置
default-time-zone='+00:00'
```

### 数据库用户配置
```sql
-- 创建数据库
CREATE DATABASE bengtai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（如果不存在）
CREATE USER 'bengtai'@'%' IDENTIFIED BY 'weizhen258';
GRANT ALL PRIVILEGES ON bengtai.* TO 'bengtai'@'%';
FLUSH PRIVILEGES;
```

## 域名和SSL配置

### 域名解析配置
```bash
# 英语站点
petcare-usa.com -> 服务器IP
www.petcare-usa.com -> 服务器IP

# 德语站点
haustier-deutschland.de -> 服务器IP
www.haustier-deutschland.de -> 服务器IP

# 俄语站点
domashnie-zhivotnye.ru -> 服务器IP
www.domashnie-zhivotnye.ru -> 服务器IP

# 管理后台
admin.petcare-usa.com -> 服务器IP
```

### SSL证书配置
```bash
# 在宝塔面板中为每个域名申请Let's Encrypt证书
# 或上传自有SSL证书
# 确保所有域名都启用HTTPS
```

## Nginx配置

### 主配置文件
```nginx
# /www/server/nginx/conf/nginx.conf
user www www;
worker_processes auto;
error_log /www/wwwlogs/nginx_error.log crit;
pid /www/server/nginx/logs/nginx.pid;
worker_rlimit_nofile 51200;

events {
    use epoll;
    worker_connections 51200;
    multi_accept on;
}

http {
    include mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                   '$status $body_bytes_sent "$http_referer" '
                   '"$http_user_agent" "$http_x_forwarded_for"';
    
    # 性能优化
    sendfile on;
    tcp_nopush on;
    keepalive_timeout 60;
    tcp_nodelay on;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1k;
    gzip_buffers 4 16k;
    gzip_http_version 1.1;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 包含站点配置
    include /www/server/panel/vhost/nginx/*.conf;
}
```

### 英语站点配置
```nginx
# /www/server/panel/vhost/nginx/petcare-usa.com.conf
server {
    listen 80;
    server_name petcare-usa.com www.petcare-usa.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name petcare-usa.com www.petcare-usa.com;
    
    # SSL配置
    ssl_certificate /www/server/panel/vhost/cert/petcare-usa.com/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/petcare-usa.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 网站根目录
    root /www/wwwroot/pet-blog-system/frontend/dist/en;
    index index.html;
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }
    
    # API代理
    location /api {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 上传文件代理
    location /uploads {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 安全头部
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
}
```

### 德语和俄语站点配置
```nginx
# 德语站点 - haustier-deutschland.de.conf
# 俄语站点 - domashnie-zhivotnye.ru.conf
# 配置结构与英语站点相同，只需修改：
# - server_name
# - SSL证书路径
# - root目录 (dist/de 或 dist/ru)
```

### 管理后台配置
```nginx
# /www/server/panel/vhost/nginx/admin.petcare-usa.com.conf
server {
    listen 80;
    server_name admin.petcare-usa.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name admin.petcare-usa.com;
    
    # SSL配置（同上）
    
    # 管理后台根目录
    root /www/wwwroot/pet-blog-system/admin/dist;
    index index.html;
    
    # API代理
    location /api {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 额外安全限制
    location /api/admin {
        # IP白名单（可选）
        # allow ***********/24;
        # deny all;
        
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 后端部署配置

### PM2配置文件
```javascript
// /www/wwwroot/pet-blog-system/backend/ecosystem.config.js
module.exports = {
  apps: [{
    name: 'pet-blog-api',
    script: './dist/app.js',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_date_format: 'YYYY-MM-DD HH:mm Z',
    error_file: '/www/wwwlogs/pet-blog-api-error.log',
    out_file: '/www/wwwlogs/pet-blog-api-out.log',
    log_file: '/www/wwwlogs/pet-blog-api.log',
    max_memory_restart: '1G',
    node_args: '--max_old_space_size=1024'
  }]
};
```

### 生产环境变量
```bash
# /www/wwwroot/pet-blog-system/backend/.env.production
DATABASE_URL="mysql://bengtai:weizhen258@127.0.0.1:3306/bengtai"
JWT_SECRET="production-super-secret-jwt-key-here"
JWT_EXPIRES_IN="24h"

TRANSLATION_API_URL="https://ai.wanderintree.top"
TRANSLATION_API_KEY="sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d"
TRANSLATION_MODEL="gemini-2.5-pro"

PORT=3000
NODE_ENV="production"

UPLOAD_DIR="/www/wwwroot/pet-blog-system/backend/uploads"
MAX_FILE_SIZE=10485760

DOMAIN_LANGUAGE_MAP='{"petcare-usa.com":"en","www.petcare-usa.com":"en","haustier-deutschland.de":"de","www.haustier-deutschland.de":"de","domashnie-zhivotnye.ru":"ru","www.domashnie-zhivotnye.ru":"ru"}'

ALLOWED_ORIGINS="https://petcare-usa.com,https://www.petcare-usa.com,https://haustier-deutschland.de,https://www.haustier-deutschland.de,https://domashnie-zhivotnye.ru,https://www.domashnie-zhivotnye.ru,https://admin.petcare-usa.com"
```

## 部署脚本

### 自动部署脚本
```bash
#!/bin/bash
# /www/wwwroot/pet-blog-system/deploy.sh

set -e

echo "开始部署宠物博客系统..."

# 进入项目目录
cd /www/wwwroot/pet-blog-system

# 拉取最新代码
git pull origin main

# 后端部署
echo "部署后端..."
cd backend
npm install --production
npm run build

# 数据库迁移
npx prisma migrate deploy
npx prisma generate

# 重启后端服务
pm2 restart pet-blog-api

# 前端部署
echo "部署前端..."
cd ../frontend

# 构建英语版本
npm install
npm run build:en
rm -rf /www/wwwroot/pet-blog-system/frontend/dist/en/*
cp -r dist/* /www/wwwroot/pet-blog-system/frontend/dist/en/

# 构建德语版本
npm run build:de
rm -rf /www/wwwroot/pet-blog-system/frontend/dist/de/*
cp -r dist/* /www/wwwroot/pet-blog-system/frontend/dist/de/

# 构建俄语版本
npm run build:ru
rm -rf /www/wwwroot/pet-blog-system/frontend/dist/ru/*
cp -r dist/* /www/wwwroot/pet-blog-system/frontend/dist/ru/

# 管理后台部署
echo "部署管理后台..."
cd ../admin
npm install
npm run build
rm -rf /www/wwwroot/pet-blog-system/admin/dist/*
cp -r dist/* /www/wwwroot/pet-blog-system/admin/dist/

# 重载Nginx配置
nginx -t && nginx -s reload

echo "部署完成！"
```

### 备份脚本
```bash
#!/bin/bash
# /www/wwwroot/pet-blog-system/backup.sh

BACKUP_DIR="/www/backup/pet-blog-system"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u bengtai -pweizhen258 bengtai > $BACKUP_DIR/database_$DATE.sql

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz /www/wwwroot/pet-blog-system/backend/uploads

# 备份代码
tar -czf $BACKUP_DIR/code_$DATE.tar.gz /www/wwwroot/pet-blog-system --exclude=node_modules --exclude=dist --exclude=uploads

# 清理7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "备份完成: $DATE"
```

## 监控和维护

### 系统监控
```bash
# 在宝塔面板中设置监控项目：
# - CPU使用率 > 80%
# - 内存使用率 > 85%
# - 磁盘使用率 > 90%
# - 网站响应时间 > 3秒
```

### 日志管理
```bash
# 设置日志轮转
# /etc/logrotate.d/pet-blog-system
/www/wwwlogs/pet-blog-*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www www
    postrotate
        pm2 reloadLogs
    endscript
}
```

### 定时任务
```bash
# 在宝塔面板计划任务中添加：
# 每天凌晨2点备份
0 2 * * * /www/wwwroot/pet-blog-system/backup.sh

# 每小时清理临时文件
0 * * * * find /tmp -name "*.tmp" -mtime +1 -delete

# 每天重启PM2（可选）
0 4 * * * pm2 restart all
```

这个部署配置指南提供了完整的生产环境部署方案，确保系统稳定运行。
