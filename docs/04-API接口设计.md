# API接口设计文档

## API概览

### 基础信息
- **Base URL**: `https://api.petcare-system.com`
- **API版本**: v1
- **认证方式**: JWT Token (管理端)
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-15T10:00:00Z"
}

// 错误响应
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "参数验证失败",
    "details": []
  },
  "timestamp": "2024-01-15T10:00:00Z"
}
```

## 前端公共API

### 1. 文章相关接口

#### 1.1 获取文章列表
```
GET /api/articles
```

**请求参数**:
```json
{
  "language": "en",           // 必需，语言代码
  "category": "dogs",         // 可选，分类slug
  "page": 1,                  // 可选，页码，默认1
  "limit": 10,                // 可选，每页数量，默认10
  "sort": "publish_date",     // 可选，排序字段
  "order": "desc"             // 可选，排序方向
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "articles": [
      {
        "id": 1,
        "title": "How to Train Basic Dog Commands",
        "slug": "basic-dog-commands",
        "excerpt": "Learn the essential commands every dog should know...",
        "featuredImage": "/images/dog-training-basic.jpg",
        "category": {
          "id": 1,
          "name": "Dog Training",
          "slug": "dog-training"
        },
        "publishDate": "2024-01-15T10:00:00Z",
        "readTime": 5
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 10,
      "totalItems": 95,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

#### 1.2 获取文章详情
```
GET /api/articles/:slug
```

**请求参数**:
```json
{
  "language": "en"            // 必需，语言代码
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "article": {
      "id": 1,
      "title": "How to Train Basic Dog Commands",
      "slug": "basic-dog-commands",
      "content": "<p>Training your dog basic commands...</p>",
      "excerpt": "Learn the essential commands...",
      "featuredImage": "/images/dog-training-basic.jpg",
      "category": {
        "id": 1,
        "name": "Dog Training",
        "slug": "dog-training",
        "parentCategory": {
          "id": 1,
          "name": "Dogs",
          "slug": "dogs"
        }
      },
      "seo": {
        "metaTitle": "How to Train Basic Dog Commands - Pet Care Expert",
        "metaDescription": "Learn essential dog training commands...",
        "metaKeywords": "dog training, basic commands, pet care"
      },
      "publishDate": "2024-01-15T10:00:00Z",
      "updatedDate": "2024-01-15T10:00:00Z",
      "readTime": 5,
      "relatedArticles": [
        {
          "id": 2,
          "title": "Advanced Dog Training Techniques",
          "slug": "advanced-dog-training",
          "featuredImage": "/images/advanced-training.jpg"
        }
      ]
    }
  }
}
```

### 2. 分类相关接口

#### 2.1 获取分类列表
```
GET /api/categories
```

**请求参数**:
```json
{
  "language": "en",           // 必需，语言代码
  "parent": null              // 可选，父分类ID，null获取顶级分类
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "Dogs",
        "slug": "dogs",
        "description": "Everything about dog care and training",
        "articleCount": 45,
        "children": [
          {
            "id": 2,
            "name": "Dog Training",
            "slug": "dog-training",
            "articleCount": 15
          },
          {
            "id": 3,
            "name": "Dog Health",
            "slug": "dog-health",
            "articleCount": 20
          }
        ]
      },
      {
        "id": 4,
        "name": "Cats",
        "slug": "cats",
        "description": "Everything about cat care and behavior",
        "articleCount": 38,
        "children": [
          {
            "id": 5,
            "name": "Cat Behavior",
            "slug": "cat-behavior",
            "articleCount": 18
          }
        ]
      }
    ]
  }
}
```

#### 2.2 获取分类下的文章
```
GET /api/categories/:slug/articles
```

**请求参数**:
```json
{
  "language": "en",           // 必需，语言代码
  "page": 1,                  // 可选，页码
  "limit": 10                 // 可选，每页数量
}
```

### 3. 搜索接口

#### 3.1 文章搜索
```
GET /api/search
```

**请求参数**:
```json
{
  "q": "dog training",        // 必需，搜索关键词
  "language": "en",           // 必需，语言代码
  "category": "dogs",         // 可选，限制分类
  "page": 1,                  // 可选，页码
  "limit": 10                 // 可选，每页数量
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": 1,
        "title": "How to Train Basic Dog Commands",
        "slug": "basic-dog-commands",
        "excerpt": "Learn the essential commands...",
        "featuredImage": "/images/dog-training-basic.jpg",
        "category": {
          "name": "Dog Training",
          "slug": "dog-training"
        },
        "relevanceScore": 0.95
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 25
    },
    "searchInfo": {
      "query": "dog training",
      "searchTime": 0.045,
      "suggestions": ["dog training tips", "puppy training"]
    }
  }
}
```

### 4. 评论相关接口

#### 4.1 获取文章评论
```
GET /api/articles/:articleId/comments
```

**请求参数**:
```json
{
  "language": "en",           // 必需，语言代码
  "page": 1,                  // 可选，页码
  "limit": 20                 // 可选，每页数量
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "comments": [
      {
        "id": 1,
        "authorName": "John Doe",
        "content": "Great article! Very helpful tips.",
        "createdAt": "2024-01-15T10:00:00Z",
        "replies": [
          {
            "id": 2,
            "authorName": "Admin",
            "content": "Thank you for your feedback!",
            "createdAt": "2024-01-15T11:00:00Z",
            "isAdmin": true
          }
        ]
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalItems": 35
    }
  }
}
```

#### 4.2 提交评论
```
POST /api/comments
```

**请求体**:
```json
{
  "articleId": 1,             // 必需，文章ID
  "language": "en",           // 必需，语言代码
  "authorName": "John Doe",   // 必需，评论者姓名
  "authorEmail": "<EMAIL>", // 必需，评论者邮箱
  "content": "Great article!", // 必需，评论内容
  "parentId": null            // 可选，父评论ID（回复评论时使用）
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "comment": {
      "id": 123,
      "status": "pending",
      "message": "评论已提交，等待审核"
    }
  }
}
```

### 5. 站点配置接口

#### 5.1 获取站点配置
```
GET /api/site/config
```

**请求参数**:
```json
{
  "language": "en"            // 必需，语言代码
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "site": {
      "name": "Pet Care Expert",
      "description": "Professional pet care knowledge platform",
      "logo": "/images/logo.png",
      "favicon": "/images/favicon.ico",
      "googleAnalytics": "GA_TRACKING_ID",
      "googleAdsense": {
        "enabled": true,
        "publisherId": "ca-pub-xxxxxxxxxx",
        "slots": {
          "header": "1234567890",
          "sidebar": "0987654321",
          "footer": "1122334455"
        }
      },
      "social": {
        "facebook": "https://facebook.com/petcareexpert",
        "twitter": "https://twitter.com/petcareexpert"
      }
    }
  }
}
```

### 6. 站点地图接口

#### 6.1 获取XML站点地图
```
GET /api/sitemap.xml
```

**请求参数**:
```json
{
  "language": "en"            // 必需，语言代码
}
```

**响应**: XML格式的站点地图

## 管理后台API

### 认证相关

#### 1.1 管理员登录
```
POST /api/admin/login
```

**请求体**:
```json
{
  "username": "admin",        // 必需，用户名
  "password": "password123"   // 必需，密码
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "admin": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>"
    },
    "expiresIn": 86400
  }
}
```

#### 1.2 刷新Token
```
POST /api/admin/refresh-token
```

**请求头**:
```
Authorization: Bearer <token>
```

### 文章管理

#### 2.1 获取文章管理列表
```
GET /api/admin/articles
```

**请求参数**:
```json
{
  "page": 1,                  // 可选，页码
  "limit": 20,                // 可选，每页数量
  "status": "published",      // 可选，状态筛选
  "category": 1,              // 可选，分类筛选
  "search": "dog training"    // 可选，搜索关键词
}
```

#### 2.2 创建文章
```
POST /api/admin/articles
```

**请求体**:
```json
{
  "title": "如何训练狗狗基本指令",    // 必需，原文标题
  "content": "<p>训练狗狗...</p>", // 必需，原文内容
  "excerpt": "本文介绍...",        // 可选，摘要
  "categoryId": 1,               // 必需，分类ID
  "featuredImage": "/images/dog-training.jpg", // 可选，特色图片
  "seo": {
    "metaTitle": "狗狗训练指南",
    "metaDescription": "专业的狗狗训练方法",
    "metaKeywords": "狗狗训练,宠物护理"
  },
  "status": "draft"              // 可选，状态，默认draft
}
```

#### 2.3 翻译文章
```
POST /api/admin/articles/:id/translate
```

**请求体**:
```json
{
  "targetLanguages": ["en", "de", "ru"], // 必需，目标语言列表
  "aiModel": "gemini-2.5-pro"            // 可选，AI模型
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "translations": [
      {
        "language": "en",
        "title": "How to Train Basic Dog Commands",
        "content": "<p>Training your dog...</p>",
        "slug": "basic-dog-commands",
        "status": "draft"
      }
    ],
    "message": "翻译完成，已保存为草稿"
  }
}
```

### 评论管理

#### 3.1 获取评论管理列表
```
GET /api/admin/comments
```

**请求参数**:
```json
{
  "status": "pending",        // 可选，状态筛选
  "page": 1,                  // 可选，页码
  "limit": 20                 // 可选，每页数量
}
```

#### 3.2 审核评论
```
PUT /api/admin/comments/:id
```

**请求体**:
```json
{
  "status": "approved",       // 必需，新状态：approved/rejected
  "adminReply": "感谢您的评论！" // 可选，管理员回复
}
```

### 文件上传

#### 4.1 上传图片
```
POST /api/admin/upload
```

**请求**: multipart/form-data
- `file`: 图片文件
- `type`: 上传类型（article/category/site）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "url": "/images/articles/2024/01/dog-training-123456.jpg",
    "filename": "dog-training-123456.jpg",
    "size": 245760,
    "mimeType": "image/jpeg"
  }
}
```

### 站点设置管理

#### 5.1 获取站点设置
```
GET /api/admin/settings
```

**请求参数**:
```json
{
  "language": "en"            // 可选，特定语言设置
}
```

#### 5.2 更新站点设置
```
PUT /api/admin/settings
```

**请求体**:
```json
{
  "language": "en",           // 必需，语言代码
  "settings": {
    "siteName": "Pet Care Expert",
    "siteDescription": "Professional pet care platform",
    "googleAnalytics": "GA_TRACKING_ID",
    "googleAdsense": {
      "enabled": true,
      "publisherId": "ca-pub-xxxxxxxxxx"
    }
  }
}
```

## 错误代码说明

### 通用错误代码
- `400` - 请求参数错误
- `401` - 未授权访问
- `403` - 权限不足
- `404` - 资源不存在
- `429` - 请求频率限制
- `500` - 服务器内部错误

### 业务错误代码
- `VALIDATION_ERROR` - 数据验证失败
- `ARTICLE_NOT_FOUND` - 文章不存在
- `CATEGORY_NOT_FOUND` - 分类不存在
- `COMMENT_PENDING` - 评论待审核
- `TRANSLATION_FAILED` - 翻译失败
- `UPLOAD_FAILED` - 文件上传失败
- `LANGUAGE_NOT_SUPPORTED` - 不支持的语言

## API限流策略

### 公共API限流
- 每IP每分钟最多100次请求
- 搜索API每IP每分钟最多20次请求
- 评论提交每IP每小时最多5次

### 管理API限流
- 每Token每分钟最多200次请求
- 文件上传每Token每小时最多50次
- 翻译API每Token每小时最多10次

这个API设计确保了前后端的高效交互和数据安全。
