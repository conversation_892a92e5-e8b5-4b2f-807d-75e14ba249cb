# 数据库设计详细文档

## 数据库连接配置

### 远程数据库信息
```
主机: ************
端口: 3306
数据库: bengtai
用户名: bengtai
密码: weizhen258
版本: MySQL 9.0.1
```

### 连接字符串
```
DATABASE_URL="mysql://bengtai:weizhen258@************:3306/bengtai"
```

## 完整表结构设计

### 1. 语言配置表 (languages)
```sql
CREATE TABLE languages (
  id INT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(5) NOT NULL UNIQUE COMMENT '语言代码：en, de, ru',
  name VARCHAR(50) NOT NULL COMMENT '语言名称：English, Deutsch, Русский',
  native_name VARCHAR(50) NOT NULL COMMENT '本地语言名称',
  domain VARCHAR(100) NOT NULL UNIQUE COMMENT '绑定域名',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  sort_order INT DEFAULT 0 COMMENT '排序权重',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_code (code),
  INDEX idx_domain (domain),
  INDEX idx_active_sort (is_active, sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='语言配置表';

-- 初始数据
INSERT INTO languages (code, name, native_name, domain, sort_order) VALUES
('en', 'English', 'English', 'petcare-usa.com', 1),
('de', 'German', 'Deutsch', 'haustier-deutschland.de', 2),
('ru', 'Russian', 'Русский', 'domashnie-zhivotnye.ru', 3);
```

### 2. 分类表 (categories)
```sql
CREATE TABLE categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  parent_id INT NULL COMMENT '父分类ID，NULL为顶级分类',
  slug VARCHAR(100) NOT NULL UNIQUE COMMENT '分类标识符',
  icon VARCHAR(100) NULL COMMENT '分类图标',
  sort_order INT DEFAULT 0 COMMENT '排序权重',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
  INDEX idx_parent_id (parent_id),
  INDEX idx_slug (slug),
  INDEX idx_active_sort (is_active, sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';

-- 初始分类数据
INSERT INTO categories (id, parent_id, slug, sort_order) VALUES
(1, NULL, 'dogs', 1),
(2, NULL, 'cats', 2),
(3, 1, 'dog-training', 1),
(4, 1, 'dog-health', 2),
(5, 1, 'dog-breeds', 3),
(6, 1, 'dog-care', 4),
(7, 2, 'cat-behavior', 1),
(8, 2, 'cat-health', 2),
(9, 2, 'cat-breeds', 3),
(10, 2, 'cat-care', 4);
```

### 3. 分类翻译表 (category_translations)
```sql
CREATE TABLE category_translations (
  id INT PRIMARY KEY AUTO_INCREMENT,
  category_id INT NOT NULL,
  language_code VARCHAR(5) NOT NULL,
  name VARCHAR(100) NOT NULL COMMENT '分类名称',
  description TEXT COMMENT '分类描述',
  meta_title VARCHAR(200) COMMENT 'SEO标题',
  meta_description VARCHAR(300) COMMENT 'SEO描述',
  meta_keywords VARCHAR(500) COMMENT 'SEO关键词',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
  FOREIGN KEY (language_code) REFERENCES languages(code) ON DELETE CASCADE,
  UNIQUE KEY unique_category_language (category_id, language_code),
  INDEX idx_language_code (language_code),
  INDEX idx_category_id (category_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类翻译表';
```

### 4. 文章表 (articles)
```sql
CREATE TABLE articles (
  id INT PRIMARY KEY AUTO_INCREMENT,
  original_title VARCHAR(300) NOT NULL COMMENT '原始标题（中文）',
  original_content LONGTEXT NOT NULL COMMENT '原始内容（中文）',
  original_excerpt TEXT COMMENT '原始摘要（中文）',
  slug VARCHAR(200) NOT NULL UNIQUE COMMENT '原始slug',
  category_id INT NOT NULL,
  featured_image VARCHAR(500) COMMENT '特色图片路径',
  image_alt VARCHAR(200) COMMENT '图片alt文本',
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  publish_date TIMESTAMP NULL COMMENT '发布时间',
  view_count INT DEFAULT 0 COMMENT '浏览次数',
  comment_count INT DEFAULT 0 COMMENT '评论数量',
  read_time INT DEFAULT 0 COMMENT '预估阅读时间（分钟）',
  author_id INT DEFAULT 1 COMMENT '作者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
  INDEX idx_status (status),
  INDEX idx_publish_date (publish_date),
  INDEX idx_category_id (category_id),
  INDEX idx_view_count (view_count),
  FULLTEXT idx_original_content (original_title, original_content, original_excerpt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章表';
```

### 5. 文章翻译表 (article_translations)
```sql
CREATE TABLE article_translations (
  id INT PRIMARY KEY AUTO_INCREMENT,
  article_id INT NOT NULL,
  language_code VARCHAR(5) NOT NULL,
  title VARCHAR(300) NOT NULL COMMENT '翻译后标题',
  slug VARCHAR(200) NOT NULL COMMENT '本地化slug',
  content LONGTEXT NOT NULL COMMENT '翻译后内容',
  excerpt TEXT COMMENT '翻译后摘要',
  meta_title VARCHAR(200) COMMENT 'SEO标题',
  meta_description VARCHAR(300) COMMENT 'SEO描述',
  meta_keywords VARCHAR(500) COMMENT 'SEO关键词',
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  translation_status ENUM('pending', 'translated', 'reviewed', 'published') DEFAULT 'pending',
  ai_translated BOOLEAN DEFAULT FALSE COMMENT '是否AI翻译',
  human_reviewed BOOLEAN DEFAULT FALSE COMMENT '是否人工校对',
  translation_notes TEXT COMMENT '翻译备注',
  published_at TIMESTAMP NULL COMMENT '发布时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
  FOREIGN KEY (language_code) REFERENCES languages(code) ON DELETE CASCADE,
  UNIQUE KEY unique_article_language (article_id, language_code),
  UNIQUE KEY unique_language_slug (language_code, slug),
  INDEX idx_language_code (language_code),
  INDEX idx_status (status),
  INDEX idx_translation_status (translation_status),
  INDEX idx_published_at (published_at),
  FULLTEXT idx_content (title, content, excerpt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章翻译表';
```

### 6. 评论表 (comments)
```sql
CREATE TABLE comments (
  id INT PRIMARY KEY AUTO_INCREMENT,
  article_id INT NOT NULL,
  language_code VARCHAR(5) NOT NULL,
  parent_id INT NULL COMMENT '父评论ID，支持多层嵌套',
  author_name VARCHAR(100) NOT NULL COMMENT '评论者姓名',
  author_email VARCHAR(200) NOT NULL COMMENT '评论者邮箱',
  content TEXT NOT NULL COMMENT '评论内容',
  status ENUM('pending', 'approved', 'rejected', 'spam') DEFAULT 'pending',
  ip_address VARCHAR(45) COMMENT 'IP地址（支持IPv6）',
  user_agent TEXT COMMENT '用户代理',
  is_admin_reply BOOLEAN DEFAULT FALSE COMMENT '是否管理员回复',
  admin_reply TEXT COMMENT '管理员回复内容',
  reply_count INT DEFAULT 0 COMMENT '回复数量',
  like_count INT DEFAULT 0 COMMENT '点赞数量',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
  FOREIGN KEY (language_code) REFERENCES languages(code) ON DELETE CASCADE,
  FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
  INDEX idx_article_language (article_id, language_code),
  INDEX idx_status (status),
  INDEX idx_parent_id (parent_id),
  INDEX idx_created_at (created_at),
  INDEX idx_author_email (author_email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论表';
```

### 7. 站点配置表 (site_settings)
```sql
CREATE TABLE site_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  language_code VARCHAR(5) NOT NULL,
  setting_key VARCHAR(100) NOT NULL COMMENT '配置键名',
  setting_value TEXT COMMENT '配置值',
  setting_type ENUM('text', 'textarea', 'boolean', 'json', 'number') DEFAULT 'text',
  setting_group VARCHAR(50) DEFAULT 'general' COMMENT '配置分组',
  description VARCHAR(200) COMMENT '配置说明',
  is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开（前端可访问）',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (language_code) REFERENCES languages(code) ON DELETE CASCADE,
  UNIQUE KEY unique_language_setting (language_code, setting_key),
  INDEX idx_language_code (language_code),
  INDEX idx_setting_group (setting_group),
  INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='站点配置表';
```

### 8. 管理员表 (admins)
```sql
CREATE TABLE admins (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
  email VARCHAR(200) NOT NULL UNIQUE COMMENT '邮箱',
  password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
  full_name VARCHAR(100) COMMENT '真实姓名',
  avatar VARCHAR(500) COMMENT '头像路径',
  role ENUM('admin', 'editor') DEFAULT 'admin' COMMENT '角色',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  last_login TIMESTAMP NULL COMMENT '最后登录时间',
  login_count INT DEFAULT 0 COMMENT '登录次数',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_username (username),
  INDEX idx_email (email),
  INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 默认管理员账户
INSERT INTO admins (username, email, password_hash, full_name, role) VALUES
('admin', '<EMAIL>', '$2b$10$hash_here', '系统管理员', 'admin');
```

### 9. 翻译任务表 (translation_tasks)
```sql
CREATE TABLE translation_tasks (
  id INT PRIMARY KEY AUTO_INCREMENT,
  article_id INT NOT NULL,
  source_language VARCHAR(5) DEFAULT 'zh' COMMENT '源语言',
  target_language VARCHAR(5) NOT NULL COMMENT '目标语言',
  status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
  ai_model VARCHAR(50) COMMENT '使用的AI模型',
  api_request_id VARCHAR(100) COMMENT 'API请求ID',
  error_message TEXT COMMENT '错误信息',
  tokens_used INT DEFAULT 0 COMMENT '使用的token数量',
  processing_time INT DEFAULT 0 COMMENT '处理时间（秒）',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
  FOREIGN KEY (target_language) REFERENCES languages(code) ON DELETE CASCADE,
  INDEX idx_article_id (article_id),
  INDEX idx_status (status),
  INDEX idx_target_language (target_language)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='翻译任务表';
```

### 10. 媒体文件表 (media_files)
```sql
CREATE TABLE media_files (
  id INT PRIMARY KEY AUTO_INCREMENT,
  filename VARCHAR(255) NOT NULL COMMENT '文件名',
  original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
  file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
  file_size INT NOT NULL COMMENT '文件大小（字节）',
  mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
  file_type ENUM('image', 'document', 'video', 'audio') DEFAULT 'image',
  width INT COMMENT '图片宽度',
  height INT COMMENT '图片高度',
  alt_text VARCHAR(200) COMMENT 'Alt文本',
  usage_count INT DEFAULT 0 COMMENT '使用次数',
  uploaded_by INT COMMENT '上传者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (uploaded_by) REFERENCES admins(id) ON DELETE SET NULL,
  INDEX idx_file_type (file_type),
  INDEX idx_mime_type (mime_type),
  INDEX idx_uploaded_by (uploaded_by),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='媒体文件表';
```

### 11. 系统日志表 (system_logs)
```sql
CREATE TABLE system_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  level ENUM('info', 'warning', 'error', 'debug') DEFAULT 'info',
  category VARCHAR(50) NOT NULL COMMENT '日志分类',
  message TEXT NOT NULL COMMENT '日志消息',
  context JSON COMMENT '上下文数据',
  user_id INT COMMENT '相关用户ID',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES admins(id) ON DELETE SET NULL,
  INDEX idx_level (level),
  INDEX idx_category (category),
  INDEX idx_created_at (created_at),
  INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';
```

## 数据库视图

### 1. 文章列表视图
```sql
CREATE VIEW v_article_list AS
SELECT 
  a.id,
  a.slug as original_slug,
  a.category_id,
  a.featured_image,
  a.status,
  a.publish_date,
  a.view_count,
  a.comment_count,
  a.created_at,
  at.language_code,
  at.title,
  at.slug,
  at.excerpt,
  at.meta_title,
  at.meta_description,
  at.status as translation_status,
  at.published_at,
  c.slug as category_slug,
  ct.name as category_name,
  pc.slug as parent_category_slug,
  pct.name as parent_category_name
FROM articles a
LEFT JOIN article_translations at ON a.id = at.article_id
LEFT JOIN categories c ON a.category_id = c.id
LEFT JOIN category_translations ct ON c.id = ct.category_id AND at.language_code = ct.language_code
LEFT JOIN categories pc ON c.parent_id = pc.id
LEFT JOIN category_translations pct ON pc.id = pct.category_id AND at.language_code = pct.language_code;
```

### 2. 评论树形视图
```sql
CREATE VIEW v_comment_tree AS
WITH RECURSIVE comment_tree AS (
  -- 根评论
  SELECT 
    id, article_id, language_code, parent_id, author_name, 
    content, status, created_at, 0 as level, 
    CAST(id AS CHAR(255)) as path
  FROM comments 
  WHERE parent_id IS NULL
  
  UNION ALL
  
  -- 子评论
  SELECT 
    c.id, c.article_id, c.language_code, c.parent_id, c.author_name,
    c.content, c.status, c.created_at, ct.level + 1,
    CONCAT(ct.path, '-', c.id)
  FROM comments c
  INNER JOIN comment_tree ct ON c.parent_id = ct.id
)
SELECT * FROM comment_tree;
```

## 存储过程

### 1. 更新文章统计
```sql
DELIMITER //
CREATE PROCEDURE UpdateArticleStats(IN article_id INT)
BEGIN
  DECLARE comment_count INT DEFAULT 0;
  
  -- 计算评论数量
  SELECT COUNT(*) INTO comment_count 
  FROM comments 
  WHERE article_id = article_id AND status = 'approved';
  
  -- 更新文章统计
  UPDATE articles 
  SET comment_count = comment_count,
      updated_at = CURRENT_TIMESTAMP
  WHERE id = article_id;
END //
DELIMITER ;
```

### 2. 批量发布翻译
```sql
DELIMITER //
CREATE PROCEDURE PublishTranslations(IN article_id INT)
BEGIN
  DECLARE done INT DEFAULT FALSE;
  DECLARE lang_code VARCHAR(5);
  DECLARE cur CURSOR FOR 
    SELECT language_code FROM article_translations 
    WHERE article_id = article_id AND status = 'draft' AND human_reviewed = TRUE;
  DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
  
  OPEN cur;
  
  read_loop: LOOP
    FETCH cur INTO lang_code;
    IF done THEN
      LEAVE read_loop;
    END IF;
    
    UPDATE article_translations 
    SET status = 'published', 
        published_at = CURRENT_TIMESTAMP,
        translation_status = 'published'
    WHERE article_id = article_id AND language_code = lang_code;
  END LOOP;
  
  CLOSE cur;
  
  -- 更新主文章状态
  UPDATE articles SET status = 'published' WHERE id = article_id;
END //
DELIMITER ;
```

## 数据库优化

### 索引优化策略
```sql
-- 复合索引优化查询
CREATE INDEX idx_article_category_status ON articles(category_id, status, publish_date DESC);
CREATE INDEX idx_translation_lang_status ON article_translations(language_code, status, published_at DESC);
CREATE INDEX idx_comment_article_status ON comments(article_id, language_code, status, created_at DESC);

-- 全文搜索索引
ALTER TABLE article_translations ADD FULLTEXT(title, content, excerpt);
ALTER TABLE category_translations ADD FULLTEXT(name, description);
```

### 分区策略（可选）
```sql
-- 按年份分区日志表
ALTER TABLE system_logs PARTITION BY RANGE (YEAR(created_at)) (
  PARTITION p2024 VALUES LESS THAN (2025),
  PARTITION p2025 VALUES LESS THAN (2026),
  PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 数据初始化脚本

### 基础配置数据
```sql
-- 插入默认站点配置
INSERT INTO site_settings (language_code, setting_key, setting_value, setting_type, setting_group, is_public) VALUES
-- 英语站点配置
('en', 'site_name', 'Pet Care Expert', 'text', 'general', true),
('en', 'site_description', 'Professional pet care knowledge platform', 'textarea', 'general', true),
('en', 'google_analytics', '', 'text', 'tracking', false),
('en', 'google_adsense_enabled', 'false', 'boolean', 'ads', false),
('en', 'google_adsense_publisher_id', '', 'text', 'ads', false),

-- 德语站点配置
('de', 'site_name', 'Haustier-Experte', 'text', 'general', true),
('de', 'site_description', 'Professionelle Plattform für Haustierpflege-Wissen', 'textarea', 'general', true),
('de', 'google_analytics', '', 'text', 'tracking', false),
('de', 'google_adsense_enabled', 'false', 'boolean', 'ads', false),

-- 俄语站点配置
('ru', 'site_name', 'Эксперт по уходу за домашними животными', 'text', 'general', true),
('ru', 'site_description', 'Профессиональная платформа знаний по уходу за домашними животными', 'textarea', 'general', true),
('ru', 'google_analytics', '', 'text', 'tracking', false),
('ru', 'google_adsense_enabled', 'false', 'boolean', 'ads', false);
```

### 分类翻译数据
```sql
-- 插入分类翻译
INSERT INTO category_translations (category_id, language_code, name, description, meta_title, meta_description) VALUES
-- 英语分类
(1, 'en', 'Dogs', 'Everything about dog care, training, and health', 'Dog Care Guide - Pet Care Expert', 'Comprehensive guide to dog care, training, health, and breeds. Expert advice for dog owners.'),
(2, 'en', 'Cats', 'Everything about cat care, behavior, and health', 'Cat Care Guide - Pet Care Expert', 'Complete guide to cat care, behavior, health, and breeds. Professional advice for cat owners.'),
(3, 'en', 'Dog Training', 'Professional dog training tips and techniques', 'Dog Training Tips - Pet Care Expert', 'Learn effective dog training methods and techniques from professional trainers.'),
(4, 'en', 'Dog Health', 'Dog health care and medical advice', 'Dog Health Care - Pet Care Expert', 'Essential dog health information, symptoms, treatments, and preventive care.'),

-- 德语分类
(1, 'de', 'Hunde', 'Alles über Hundepflege, Training und Gesundheit', 'Hundepflege Ratgeber - Haustier-Experte', 'Umfassender Ratgeber für Hundepflege, Training, Gesundheit und Rassen.'),
(2, 'de', 'Katzen', 'Alles über Katzenpflege, Verhalten und Gesundheit', 'Katzenpflege Ratgeber - Haustier-Experte', 'Vollständiger Ratgeber für Katzenpflege, Verhalten, Gesundheit und Rassen.'),
(3, 'de', 'Hundetraining', 'Professionelle Hundetraining-Tipps und Techniken', 'Hundetraining Tipps - Haustier-Experte', 'Lernen Sie effektive Hundetraining-Methoden von professionellen Trainern.'),

-- 俄语分类
(1, 'ru', 'Собаки', 'Всё об уходе за собаками, дрессировке и здоровье', 'Уход за собаками - Эксперт по домашним животным', 'Полное руководство по уходу за собаками, дрессировке, здоровью и породам.'),
(2, 'ru', 'Кошки', 'Всё об уходе за кошками, поведении и здоровье', 'Уход за кошками - Эксперт по домашним животным', 'Полное руководство по уходу за кошками, поведению, здоровью и породам.'),
(3, 'ru', 'Дрессировка собак', 'Профессиональные советы и техники дрессировки', 'Дрессировка собак - Эксперт по домашним животным', 'Изучите эффективные методы дрессировки от профессиональных тренеров.');
```

## 备份与恢复策略

### 自动备份脚本
```bash
#!/bin/bash
# 数据库备份脚本
BACKUP_DIR="/var/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="bengtai"
DB_HOST="************"
DB_USER="bengtai"
DB_PASS="weizhen258"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete
```

### 数据恢复命令
```bash
# 恢复数据库
mysql -h ************ -u bengtai -pweizhen258 bengtai < backup_file.sql
```

## 性能监控

### 慢查询监控
```sql
-- 启用慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
SET GLOBAL log_queries_not_using_indexes = 'ON';

-- 查看慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;
```

### 性能优化建议
```sql
-- 查看表大小
SELECT
  table_name,
  ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables
WHERE table_schema = 'bengtai'
ORDER BY (data_length + index_length) DESC;

-- 查看索引使用情况
SELECT
  table_name,
  index_name,
  cardinality,
  sub_part,
  packed,
  nullable,
  index_type
FROM information_schema.statistics
WHERE table_schema = 'bengtai';
```

这个数据库设计支持多语言、高性能查询和数据完整性，为整个站群系统提供了坚实的数据基础。
