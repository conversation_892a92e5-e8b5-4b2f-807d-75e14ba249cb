# 后端开发规范文档

## Node.js + Express 项目结构

### 项目目录结构
```
backend/
├── src/
│   ├── controllers/        # 控制器
│   │   ├── admin/         # 管理后台控制器
│   │   └── public/        # 公共API控制器
│   ├── middleware/        # 中间件
│   ├── models/           # 数据模型 (Prisma)
│   ├── routes/           # 路由定义
│   │   ├── admin/        # 管理后台路由
│   │   └── public/       # 公共API路由
│   ├── services/         # 业务逻辑服务
│   ├── utils/            # 工具函数
│   ├── config/           # 配置文件
│   ├── types/            # TypeScript类型定义
│   └── app.ts            # 应用入口
├── prisma/               # Prisma配置
│   ├── schema.prisma     # 数据库模式
│   └── migrations/       # 数据库迁移
├── uploads/              # 文件上传目录
├── logs/                 # 日志文件
├── tests/                # 测试文件
├── .env                  # 环境变量
├── package.json
└── tsconfig.json
```

### 环境配置

#### .env文件配置
```env
# 数据库配置
DATABASE_URL="mysql://bengtai:weizhen258@************:3306/bengtai"

# JWT配置
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_EXPIRES_IN="24h"

# 翻译API配置
TRANSLATION_API_URL="https://ai.wanderintree.top"
TRANSLATION_API_KEY="sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d"
TRANSLATION_MODEL="gemini-2.5-pro"

# 服务器配置
PORT=3000
NODE_ENV="development"

# 文件上传配置
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=10485760  # 10MB

# 域名语言映射
DOMAIN_LANGUAGE_MAP='{"petcare-usa.com":"en","haustier-deutschland.de":"de","domashnie-zhivotnye.ru":"ru"}'

# CORS配置
ALLOWED_ORIGINS="http://localhost:4321,https://petcare-usa.com,https://haustier-deutschland.de,https://domashnie-zhivotnye.ru"
```

### Prisma配置

#### schema.prisma
```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Language {
  id        Int      @id @default(autoincrement())
  code      String   @unique @db.VarChar(5)
  name      String   @db.VarChar(50)
  nativeName String  @map("native_name") @db.VarChar(50)
  domain    String   @unique @db.VarChar(100)
  isActive  Boolean  @default(true) @map("is_active")
  sortOrder Int      @default(0) @map("sort_order")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  categoryTranslations CategoryTranslation[]
  articleTranslations  ArticleTranslation[]
  comments            Comment[]
  siteSettings        SiteSetting[]

  @@map("languages")
}

model Category {
  id        Int      @id @default(autoincrement())
  parentId  Int?     @map("parent_id")
  slug      String   @unique @db.VarChar(100)
  icon      String?  @db.VarChar(100)
  sortOrder Int      @default(0) @map("sort_order")
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 自关联
  parent   Category?  @relation("CategoryParent", fields: [parentId], references: [id])
  children Category[] @relation("CategoryParent")

  // 关联关系
  translations CategoryTranslation[]
  articles     Article[]

  @@map("categories")
}

model CategoryTranslation {
  id              Int      @id @default(autoincrement())
  categoryId      Int      @map("category_id")
  languageCode    String   @map("language_code") @db.VarChar(5)
  name            String   @db.VarChar(100)
  description     String?  @db.Text
  metaTitle       String?  @map("meta_title") @db.VarChar(200)
  metaDescription String?  @map("meta_description") @db.VarChar(300)
  metaKeywords    String?  @map("meta_keywords") @db.VarChar(500)
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // 关联关系
  category Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  language Language @relation(fields: [languageCode], references: [code], onDelete: Cascade)

  @@unique([categoryId, languageCode], name: "unique_category_language")
  @@map("category_translations")
}

model Article {
  id              Int       @id @default(autoincrement())
  originalTitle   String    @map("original_title") @db.VarChar(300)
  originalContent String    @map("original_content") @db.LongText
  originalExcerpt String?   @map("original_excerpt") @db.Text
  slug            String    @unique @db.VarChar(200)
  categoryId      Int       @map("category_id")
  featuredImage   String?   @map("featured_image") @db.VarChar(500)
  imageAlt        String?   @map("image_alt") @db.VarChar(200)
  status          Status    @default(DRAFT)
  publishDate     DateTime? @map("publish_date")
  viewCount       Int       @default(0) @map("view_count")
  commentCount    Int       @default(0) @map("comment_count")
  readTime        Int       @default(0) @map("read_time")
  authorId        Int       @default(1) @map("author_id")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  // 关联关系
  category     Category             @relation(fields: [categoryId], references: [id])
  translations ArticleTranslation[]
  comments     Comment[]

  @@map("articles")
}

model ArticleTranslation {
  id                Int               @id @default(autoincrement())
  articleId         Int               @map("article_id")
  languageCode      String            @map("language_code") @db.VarChar(5)
  title             String            @db.VarChar(300)
  slug              String            @db.VarChar(200)
  content           String            @db.LongText
  excerpt           String?           @db.Text
  metaTitle         String?           @map("meta_title") @db.VarChar(200)
  metaDescription   String?           @map("meta_description") @db.VarChar(300)
  metaKeywords      String?           @map("meta_keywords") @db.VarChar(500)
  status            Status            @default(DRAFT)
  translationStatus TranslationStatus @default(PENDING) @map("translation_status")
  aiTranslated      Boolean           @default(false) @map("ai_translated")
  humanReviewed     Boolean           @default(false) @map("human_reviewed")
  translationNotes  String?           @map("translation_notes") @db.Text
  publishedAt       DateTime?         @map("published_at")
  createdAt         DateTime          @default(now()) @map("created_at")
  updatedAt         DateTime          @updatedAt @map("updated_at")

  // 关联关系
  article  Article  @relation(fields: [articleId], references: [id], onDelete: Cascade)
  language Language @relation(fields: [languageCode], references: [code], onDelete: Cascade)

  @@unique([articleId, languageCode], name: "unique_article_language")
  @@unique([languageCode, slug], name: "unique_language_slug")
  @@map("article_translations")
}

model Comment {
  id           Int           @id @default(autoincrement())
  articleId    Int           @map("article_id")
  languageCode String        @map("language_code") @db.VarChar(5)
  parentId     Int?          @map("parent_id")
  authorName   String        @map("author_name") @db.VarChar(100)
  authorEmail  String        @map("author_email") @db.VarChar(200)
  content      String        @db.Text
  status       CommentStatus @default(PENDING)
  ipAddress    String?       @map("ip_address") @db.VarChar(45)
  userAgent    String?       @map("user_agent") @db.Text
  isAdminReply Boolean       @default(false) @map("is_admin_reply")
  adminReply   String?       @map("admin_reply") @db.Text
  replyCount   Int           @default(0) @map("reply_count")
  likeCount    Int           @default(0) @map("like_count")
  createdAt    DateTime      @default(now()) @map("created_at")
  updatedAt    DateTime      @updatedAt @map("updated_at")

  // 关联关系
  article  Article   @relation(fields: [articleId], references: [id], onDelete: Cascade)
  language Language  @relation(fields: [languageCode], references: [code], onDelete: Cascade)
  parent   Comment?  @relation("CommentReplies", fields: [parentId], references: [id], onDelete: Cascade)
  replies  Comment[] @relation("CommentReplies")

  @@map("comments")
}

model SiteSetting {
  id           Int         @id @default(autoincrement())
  languageCode String      @map("language_code") @db.VarChar(5)
  settingKey   String      @map("setting_key") @db.VarChar(100)
  settingValue String?     @map("setting_value") @db.Text
  settingType  SettingType @default(TEXT) @map("setting_type")
  settingGroup String      @default("general") @map("setting_group") @db.VarChar(50)
  description  String?     @db.VarChar(200)
  isPublic     Boolean     @default(false) @map("is_public")
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")

  // 关联关系
  language Language @relation(fields: [languageCode], references: [code], onDelete: Cascade)

  @@unique([languageCode, settingKey], name: "unique_language_setting")
  @@map("site_settings")
}

model Admin {
  id         Int       @id @default(autoincrement())
  username   String    @unique @db.VarChar(50)
  email      String    @unique @db.VarChar(200)
  passwordHash String  @map("password_hash") @db.VarChar(255)
  fullName   String?   @map("full_name") @db.VarChar(100)
  avatar     String?   @db.VarChar(500)
  role       AdminRole @default(ADMIN)
  isActive   Boolean   @default(true) @map("is_active")
  lastLogin  DateTime? @map("last_login")
  loginCount Int       @default(0) @map("login_count")
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")

  @@map("admins")
}

// 枚举类型
enum Status {
  DRAFT
  PUBLISHED
  ARCHIVED

  @@map("status")
}

enum TranslationStatus {
  PENDING
  TRANSLATED
  REVIEWED
  PUBLISHED

  @@map("translation_status")
}

enum CommentStatus {
  PENDING
  APPROVED
  REJECTED
  SPAM

  @@map("comment_status")
}

enum SettingType {
  TEXT
  TEXTAREA
  BOOLEAN
  JSON
  NUMBER

  @@map("setting_type")
}

enum AdminRole {
  ADMIN
  EDITOR

  @@map("admin_role")
}
```

### Express应用配置

#### app.ts主文件
```typescript
// src/app.ts
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { PrismaClient } from '@prisma/client';

import { errorHandler } from './middleware/errorHandler';
import { languageMiddleware } from './middleware/languageMiddleware';
import { authMiddleware } from './middleware/authMiddleware';

import publicRoutes from './routes/public';
import adminRoutes from './routes/admin';

const app = express();
const prisma = new PrismaClient();

// 基础中间件
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use('/uploads', express.static('uploads'));

// 限流中间件
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100次请求
  message: 'Too many requests from this IP'
});
app.use('/api', limiter);

// 语言识别中间件
app.use(languageMiddleware);

// 路由
app.use('/api', publicRoutes);
app.use('/api/admin', authMiddleware, adminRoutes);

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// 错误处理
app.use(errorHandler);

// 优雅关闭
process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

export default app;
```

### 中间件设计

#### 语言识别中间件
```typescript
// src/middleware/languageMiddleware.ts
import { Request, Response, NextFunction } from 'express';

const DOMAIN_LANGUAGE_MAP: Record<string, string> = JSON.parse(
  process.env.DOMAIN_LANGUAGE_MAP || '{}'
);

export interface RequestWithLanguage extends Request {
  language: string;
}

export const languageMiddleware = (
  req: RequestWithLanguage,
  res: Response,
  next: NextFunction
) => {
  const host = req.get('host') || '';
  const language = DOMAIN_LANGUAGE_MAP[host] || 'en';
  
  req.language = language;
  res.setHeader('Content-Language', language);
  
  next();
};
```

#### 认证中间件
```typescript
// src/middleware/authMiddleware.ts
import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface RequestWithAdmin extends Request {
  admin?: {
    id: number;
    username: string;
    role: string;
  };
}

export const authMiddleware = async (
  req: RequestWithAdmin,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ 
        success: false, 
        error: { code: 'UNAUTHORIZED', message: 'No token provided' }
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    
    const admin = await prisma.admin.findUnique({
      where: { id: decoded.id, isActive: true },
      select: { id: true, username: true, role: true }
    });

    if (!admin) {
      return res.status(401).json({
        success: false,
        error: { code: 'UNAUTHORIZED', message: 'Invalid token' }
      });
    }

    req.admin = admin;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: { code: 'UNAUTHORIZED', message: 'Invalid token' }
    });
  }
};
```

#### 错误处理中间件
```typescript
// src/middleware/errorHandler.ts
import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';

export const errorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error('Error:', err);

  // Prisma错误处理
  if (err instanceof Prisma.PrismaClientKnownRequestError) {
    if (err.code === 'P2002') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'DUPLICATE_ENTRY',
          message: 'Record already exists'
        }
      });
    }
    
    if (err.code === 'P2025') {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Record not found'
        }
      });
    }
  }

  // 验证错误
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: err.message,
        details: err.details
      }
    });
  }

  // JWT错误
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      error: {
        code: 'INVALID_TOKEN',
        message: 'Invalid token'
      }
    });
  }

  // 默认错误
  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: 'Internal server error'
    },
    timestamp: new Date().toISOString()
  });
};
```

### 服务层设计

#### 文章服务
```typescript
// src/services/articleService.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export class ArticleService {
  // 获取文章列表
  async getArticles(params: {
    language: string;
    category?: string;
    page?: number;
    limit?: number;
    status?: string;
  }) {
    const { language, category, page = 1, limit = 10, status = 'PUBLISHED' } = params;
    
    const skip = (page - 1) * limit;
    
    const where: any = {
      translations: {
        some: {
          languageCode: language,
          status: status
        }
      }
    };

    if (category) {
      where.category = {
        OR: [
          { slug: category },
          { parent: { slug: category } }
        ]
      };
    }

    const [articles, total] = await Promise.all([
      prisma.article.findMany({
        where,
        include: {
          translations: {
            where: { languageCode: language },
            select: {
              title: true,
              slug: true,
              excerpt: true,
              metaTitle: true,
              metaDescription: true,
              publishedAt: true
            }
          },
          category: {
            include: {
              translations: {
                where: { languageCode: language },
                select: { name: true }
              },
              parent: {
                include: {
                  translations: {
                    where: { languageCode: language },
                    select: { name: true }
                  }
                }
              }
            }
          }
        },
        skip,
        take: limit,
        orderBy: { publishDate: 'desc' }
      }),
      prisma.article.count({ where })
    ]);

    return {
      articles: articles.map(article => ({
        id: article.id,
        title: article.translations[0]?.title,
        slug: article.translations[0]?.slug,
        excerpt: article.translations[0]?.excerpt,
        featuredImage: article.featuredImage,
        publishDate: article.publishDate,
        category: {
          name: article.category.translations[0]?.name,
          slug: article.category.slug,
          parent: article.category.parent ? {
            name: article.category.parent.translations[0]?.name,
            slug: article.category.parent.slug
          } : null
        },
        readTime: article.readTime,
        viewCount: article.viewCount,
        commentCount: article.commentCount
      })),
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      }
    };
  }

  // 获取文章详情
  async getArticleBySlug(slug: string, language: string) {
    const article = await prisma.article.findFirst({
      where: {
        translations: {
          some: {
            slug: slug,
            languageCode: language,
            status: 'PUBLISHED'
          }
        }
      },
      include: {
        translations: {
          where: { languageCode: language }
        },
        category: {
          include: {
            translations: {
              where: { languageCode: language }
            },
            parent: {
              include: {
                translations: {
                  where: { languageCode: language }
                }
              }
            }
          }
        }
      }
    });

    if (!article || !article.translations[0]) {
      throw new Error('Article not found');
    }

    const translation = article.translations[0];

    // 增加浏览量
    await prisma.article.update({
      where: { id: article.id },
      data: { viewCount: { increment: 1 } }
    });

    return {
      id: article.id,
      title: translation.title,
      slug: translation.slug,
      content: translation.content,
      excerpt: translation.excerpt,
      featuredImage: article.featuredImage,
      publishDate: article.publishDate,
      updatedDate: article.updatedAt,
      category: {
        id: article.category.id,
        name: article.category.translations[0]?.name,
        slug: article.category.slug,
        parent: article.category.parent ? {
          id: article.category.parent.id,
          name: article.category.parent.translations[0]?.name,
          slug: article.category.parent.slug
        } : null
      },
      seo: {
        metaTitle: translation.metaTitle,
        metaDescription: translation.metaDescription,
        metaKeywords: translation.metaKeywords
      },
      readTime: article.readTime,
      viewCount: article.viewCount + 1,
      commentCount: article.commentCount
    };
  }
}
```

这个后端开发规范提供了完整的Node.js + Express + Prisma架构基础。
