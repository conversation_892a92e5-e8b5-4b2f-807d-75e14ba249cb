# 前端设计规范文档

## Astro项目结构

### 项目目录结构
```
frontend/
├── src/
│   ├── components/          # 共享组件
│   │   ├── common/         # 通用组件
│   │   ├── layout/         # 布局组件
│   │   ├── seo/           # SEO组件
│   │   └── ui/            # UI组件
│   ├── layouts/           # 页面布局
│   ├── pages/             # 页面文件
│   ├── styles/            # 样式文件
│   ├── utils/             # 工具函数
│   ├── types/             # TypeScript类型
│   └── config/            # 配置文件
├── templates/             # 语言模板
│   ├── en/               # 英语模板
│   │   ├── pages/
│   │   ├── components/
│   │   └── content/
│   ├── de/               # 德语模板
│   └── ru/               # 俄语模板
├── public/               # 静态资源
│   ├── images/
│   ├── icons/
│   └── robots.txt
└── dist/                 # 构建输出
    ├── en/
    ├── de/
    └── ru/
```

### Astro配置文件
```javascript
// astro.config.mjs
import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import react from '@astrojs/react';
import sitemap from '@astrojs/sitemap';

export default defineConfig({
  integrations: [
    tailwind(),
    react(),
    sitemap({
      i18n: {
        defaultLocale: 'en',
        locales: {
          en: 'en',
          de: 'de', 
          ru: 'ru'
        }
      }
    })
  ],
  output: 'static',
  build: {
    format: 'directory'
  },
  vite: {
    build: {
      rollupOptions: {
        output: {
          assetFileNames: 'assets/[name].[hash][extname]'
        }
      }
    }
  }
});
```

## 页面设计规范

### 1. 首页设计 (Homepage)

#### 页面结构
```astro
---
// src/pages/index.astro
import Layout from '../layouts/Layout.astro';
import Hero from '../components/Hero.astro';
import FeaturedArticles from '../components/FeaturedArticles.astro';
import Categories from '../components/Categories.astro';
import Newsletter from '../components/Newsletter.astro';

const seo = {
  title: "Pet Care Expert - Professional Cat & Dog Care Knowledge",
  description: "Professional pet care knowledge platform providing comprehensive guidance on cat and dog health, training, and care for happier, healthier pets.",
  keywords: "pet care, dog training, cat health, pet tips, animal care"
};
---

<Layout seo={seo}>
  <Hero />
  <FeaturedArticles />
  <Categories />
  <Newsletter />
</Layout>
```

#### Hero区域设计
```astro
---
// src/components/Hero.astro
---
<section class="hero bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
  <div class="container mx-auto px-4 text-center">
    <h1 class="text-5xl font-bold mb-6">
      Professional Pet Care Knowledge
    </h1>
    <p class="text-xl mb-8 max-w-2xl mx-auto">
      Expert guidance for cat and dog health, training, and care
    </p>
    <div class="flex justify-center space-x-4">
      <a href="/dogs" class="btn btn-primary">Dog Care</a>
      <a href="/cats" class="btn btn-secondary">Cat Care</a>
    </div>
  </div>
</section>
```

### 2. 文章列表页设计

#### 分类页面结构
```astro
---
// src/pages/[category]/index.astro
export async function getStaticPaths() {
  return [
    { params: { category: 'dogs' } },
    { params: { category: 'cats' } },
    { params: { category: 'dog-training' } },
    { params: { category: 'cat-behavior' } }
  ];
}

const { category } = Astro.params;
const articles = await getArticlesByCategory(category);
const categoryInfo = await getCategoryInfo(category);
---

<Layout seo={categoryInfo.seo}>
  <CategoryHeader category={categoryInfo} />
  <ArticleGrid articles={articles} />
  <Pagination />
</Layout>
```

#### 文章网格组件
```astro
---
// src/components/ArticleGrid.astro
interface Props {
  articles: Article[];
}

const { articles } = Astro.props;
---

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
  {articles.map(article => (
    <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      <img 
        src={article.featuredImage} 
        alt={article.title}
        class="w-full h-48 object-cover"
        loading="lazy"
      />
      <div class="p-6">
        <h3 class="text-xl font-semibold mb-2">
          <a href={`/${article.categorySlug}/${article.slug}`} class="hover:text-blue-600">
            {article.title}
          </a>
        </h3>
        <p class="text-gray-600 mb-4">{article.excerpt}</p>
        <div class="flex justify-between items-center text-sm text-gray-500">
          <span>{article.publishDate}</span>
          <span>{article.readTime} min read</span>
        </div>
      </div>
    </article>
  ))}
</div>
```

### 3. 文章详情页设计

#### 文章页面结构
```astro
---
// src/pages/[category]/[slug].astro
export async function getStaticPaths() {
  const articles = await getAllArticles();
  return articles.map(article => ({
    params: { 
      category: article.categorySlug, 
      slug: article.slug 
    },
    props: { article }
  }));
}

const { article } = Astro.props;
const relatedArticles = await getRelatedArticles(article.id);
---

<Layout seo={article.seo}>
  <ArticleHeader article={article} />
  <ArticleContent content={article.content} />
  <ArticleFooter />
  <RelatedArticles articles={relatedArticles} />
  <CommentSection articleId={article.id} />
</Layout>
```

#### 文章内容组件
```astro
---
// src/components/ArticleContent.astro
interface Props {
  content: string;
}

const { content } = Astro.props;
---

<div class="prose prose-lg max-w-none">
  <!-- 广告位 - 文章开头 -->
  <div class="ad-container mb-8" data-ad-slot="article-top">
    <!-- Google AdSense 代码将在这里注入 -->
  </div>
  
  <div set:html={content} />
  
  <!-- 广告位 - 文章结尾 -->
  <div class="ad-container mt-8" data-ad-slot="article-bottom">
    <!-- Google AdSense 代码将在这里注入 -->
  </div>
</div>

<style>
.prose {
  @apply text-gray-800 leading-relaxed;
}

.prose h2 {
  @apply text-2xl font-bold mt-8 mb-4 text-gray-900;
}

.prose h3 {
  @apply text-xl font-semibold mt-6 mb-3 text-gray-900;
}

.prose p {
  @apply mb-4;
}

.prose img {
  @apply rounded-lg shadow-md my-6;
}

.prose ul, .prose ol {
  @apply my-4 pl-6;
}

.prose li {
  @apply mb-2;
}

.ad-container {
  @apply text-center;
  min-height: 250px;
  background: #f8f9fa;
  border: 1px dashed #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
```

### 4. 搜索页面设计

#### 搜索结果页
```astro
---
// src/pages/search.astro
const query = Astro.url.searchParams.get('q') || '';
const results = query ? await searchArticles(query) : [];
---

<Layout seo={{
  title: `Search Results for "${query}" - Pet Care Expert`,
  description: `Search results for ${query} on Pet Care Expert`
}}>
  <SearchHeader query={query} resultCount={results.length} />
  <SearchResults results={results} />
  <SearchSidebar />
</Layout>
```

#### 搜索组件
```react
// src/components/SearchBox.tsx
import { useState } from 'react';

export default function SearchBox() {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      window.location.href = `/search?q=${encodeURIComponent(query)}`;
    }
  };

  const fetchSuggestions = async (searchQuery: string) => {
    if (searchQuery.length > 2) {
      const response = await fetch(`/api/search/suggestions?q=${searchQuery}`);
      const data = await response.json();
      setSuggestions(data.suggestions);
    } else {
      setSuggestions([]);
    }
  };

  return (
    <div className="relative">
      <form onSubmit={handleSearch} className="flex">
        <input
          type="text"
          value={query}
          onChange={(e) => {
            setQuery(e.target.value);
            fetchSuggestions(e.target.value);
          }}
          placeholder="Search for pet care tips..."
          className="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button
          type="submit"
          className="px-6 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700"
        >
          Search
        </button>
      </form>
      
      {suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-b-lg shadow-lg z-10">
          {suggestions.map((suggestion, index) => (
            <a
              key={index}
              href={`/search?q=${encodeURIComponent(suggestion)}`}
              className="block px-4 py-2 hover:bg-gray-100"
            >
              {suggestion}
            </a>
          ))}
        </div>
      )}
    </div>
  );
}
```

## 评论系统设计

### 评论组件
```react
// src/components/CommentSection.tsx
import { useState, useEffect } from 'react';

interface Comment {
  id: number;
  authorName: string;
  content: string;
  createdAt: string;
  replies: Comment[];
}

interface Props {
  articleId: number;
  language: string;
}

export default function CommentSection({ articleId, language }: Props) {
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState({
    authorName: '',
    authorEmail: '',
    content: ''
  });

  useEffect(() => {
    fetchComments();
  }, []);

  const fetchComments = async () => {
    const response = await fetch(`/api/articles/${articleId}/comments?language=${language}`);
    const data = await response.json();
    setComments(data.comments);
  };

  const submitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const response = await fetch('/api/comments', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        articleId,
        language,
        ...newComment
      })
    });

    if (response.ok) {
      setNewComment({ authorName: '', authorEmail: '', content: '' });
      alert('Comment submitted for review!');
    }
  };

  return (
    <section className="mt-12">
      <h3 className="text-2xl font-bold mb-6">Comments</h3>
      
      {/* 评论表单 */}
      <form onSubmit={submitComment} className="mb-8 p-6 bg-gray-50 rounded-lg">
        <h4 className="text-lg font-semibold mb-4">Leave a Comment</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <input
            type="text"
            placeholder="Your Name"
            value={newComment.authorName}
            onChange={(e) => setNewComment({...newComment, authorName: e.target.value})}
            required
            className="px-3 py-2 border border-gray-300 rounded-lg"
          />
          <input
            type="email"
            placeholder="Your Email"
            value={newComment.authorEmail}
            onChange={(e) => setNewComment({...newComment, authorEmail: e.target.value})}
            required
            className="px-3 py-2 border border-gray-300 rounded-lg"
          />
        </div>
        <textarea
          placeholder="Your Comment"
          value={newComment.content}
          onChange={(e) => setNewComment({...newComment, content: e.target.value})}
          required
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg mb-4"
        />
        <button
          type="submit"
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Submit Comment
        </button>
      </form>

      {/* 评论列表 */}
      <div className="space-y-6">
        {comments.map(comment => (
          <CommentItem key={comment.id} comment={comment} />
        ))}
      </div>
    </section>
  );
}

function CommentItem({ comment }: { comment: Comment }) {
  return (
    <div className="border-l-4 border-blue-200 pl-4">
      <div className="flex items-center mb-2">
        <span className="font-semibold">{comment.authorName}</span>
        <span className="text-gray-500 ml-2 text-sm">
          {new Date(comment.createdAt).toLocaleDateString()}
        </span>
      </div>
      <p className="text-gray-700 mb-3">{comment.content}</p>
      
      {comment.replies.length > 0 && (
        <div className="ml-6 space-y-4">
          {comment.replies.map(reply => (
            <CommentItem key={reply.id} comment={reply} />
          ))}
        </div>
      )}
    </div>
  );
}
```

## SEO组件设计

### SEO头部组件
```astro
---
// src/components/SEOHead.astro
interface Props {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  language?: string;
}

const {
  title,
  description,
  keywords,
  image = '/images/default-og.jpg',
  url = Astro.url.href,
  type = 'website',
  language = 'en'
} = Astro.props;

const siteName = 'Pet Care Expert';
---

<!-- 基础SEO -->
<title>{title}</title>
<meta name="description" content={description} />
{keywords && <meta name="keywords" content={keywords} />}
<meta name="robots" content="index, follow" />
<meta name="language" content={language} />

<!-- Open Graph -->
<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta property="og:image" content={image} />
<meta property="og:url" content={url} />
<meta property="og:type" content={type} />
<meta property="og:site_name" content={siteName} />

<!-- Twitter Card -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content={title} />
<meta name="twitter:description" content={description} />
<meta name="twitter:image" content={image} />

<!-- 结构化数据 -->
<script type="application/ld+json" set:html={JSON.stringify({
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": siteName,
  "url": url,
  "description": description,
  "potentialAction": {
    "@type": "SearchAction",
    "target": `${url}/search?q={search_term_string}`,
    "query-input": "required name=search_term_string"
  }
})} />
```

### 面包屑组件
```astro
---
// src/components/Breadcrumbs.astro
interface BreadcrumbItem {
  name: string;
  url?: string;
}

interface Props {
  items: BreadcrumbItem[];
}

const { items } = Astro.props;
---

<nav aria-label="Breadcrumb" class="mb-6">
  <ol class="flex items-center space-x-2 text-sm text-gray-600">
    <li>
      <a href="/" class="hover:text-blue-600">Home</a>
    </li>
    {items.map((item, index) => (
      <li class="flex items-center">
        <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
        </svg>
        {item.url ? (
          <a href={item.url} class="hover:text-blue-600">{item.name}</a>
        ) : (
          <span class="text-gray-900">{item.name}</span>
        )}
      </li>
    ))}
  </ol>
</nav>

<!-- 结构化数据 -->
<script type="application/ld+json" set:html={JSON.stringify({
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": "/"
    },
    ...items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 2,
      "name": item.name,
      "item": item.url
    }))
  ]
})} />
```

这个前端设计规范确保了每个语言模板的一致性和SEO优化。
