# 宠物博客站群系统

一个专注于猫狗护理知识分享的多语言博客站群系统，支持英语、德语、俄语三种语言，每种语言对应独立域名和模板。

## 🎯 项目概述

### 核心特性
- **多语言支持**: 英语、德语、俄语独立模板，一语言一域名策略
- **SEO优化**: 严格按照Google SEO最佳实践设计
- **AI翻译**: 集成Gemini 2.5 Pro自动翻译系统
- **评论系统**: 支持多层嵌套评论，后台审核机制
- **管理后台**: 完整的内容管理和翻译工作流
- **响应式设计**: 完美适配桌面端和移动端

### 技术栈
- **前端**: Astro 4.x + TypeScript + Tailwind CSS
- **后端**: Node.js + Express + Prisma ORM
- **数据库**: MySQL 9.0.1
- **部署**: Debian 12 + 宝塔面板
- **AI翻译**: Gemini 2.5 Pro (OpenAI兼容API)

## 🚀 快速开始

### 环境要求
- Node.js 18.x LTS
- MySQL 9.0.1
- macOS 12.0+ (开发环境)

### 本地开发环境搭建

#### 1. 克隆项目
```bash
git clone <repository-url> pet-blog-system
cd pet-blog-system
```

#### 2. 安装依赖
```bash
# 后端依赖
cd backend
npm install

# 前端依赖
cd ../frontend
npm install

# 管理后台依赖
cd ../admin
npm install
```

#### 3. 配置数据库
```bash
# 创建本地数据库
mysql -u root -p
CREATE DATABASE pet_blog_local CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'dev_user'@'localhost' IDENTIFIED BY 'dev_password';
GRANT ALL PRIVILEGES ON pet_blog_local.* TO 'dev_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 配置环境变量
cp backend/.env.example backend/.env
# 编辑 backend/.env 文件，配置数据库连接
```

#### 4. 初始化数据库
```bash
cd backend
npx prisma migrate dev --name init
npx prisma generate
npm run db:seed
```

#### 5. 启动开发服务器
```bash
# 启动后端 (端口3000)
cd backend && npm run dev

# 启动前端 (端口4321)
cd frontend && npm run dev:en

# 启动管理后台 (端口3001)
cd admin && npm run dev
```

### 访问地址
- **前端站点**: http://localhost:4321
- **管理后台**: http://localhost:3001
- **API接口**: http://localhost:3000/api

## 📖 功能说明

### ✅ 已实现功能
- 多语言站点架构设计
- 数据库模型设计
- API接口设计
- SEO优化策略
- 翻译系统设计
- 部署配置方案

### 🚧 开发中功能
- 后端API实现
- 前端模板开发
- 管理后台开发
- AI翻译集成
- 评论系统
- 文件上传系统

### 📋 待开发功能
- 性能优化
- 安全加固
- 监控系统
- 自动化部署

## 🛠️ 技术架构

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   英语站点       │    │   德语站点       │    │   俄语站点       │
│ petcare-usa.com │    │haustier-de.com  │    │domashnie-ru.com │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      Nginx 反向代理        │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │    Node.js API 服务器     │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      MySQL 数据库        │
                    └───────────────────────────┘
```

### 多语言架构
- **域名策略**: 每种语言使用独立顶级域名
- **模板策略**: 每种语言使用独立的Astro模板
- **内容策略**: 原文+翻译的多语言内容管理
- **SEO策略**: 每种语言独立的SEO优化

## 📚 文档目录

### 核心文档
- [项目需求分析](docs/01-项目需求分析.md) - 详细的业务需求和功能规格
- [技术架构设计](docs/02-技术架构设计.md) - 系统架构和技术选型
- [SEO优化策略](docs/03-SEO优化策略.md) - Google SEO最佳实践
- [API接口设计](docs/04-API接口设计.md) - 完整的API规范文档

### 开发文档
- [数据库设计详细](docs/05-数据库设计详细.md) - 数据库模型和关系设计
- [前端设计规范](docs/06-前端设计规范.md) - 前端组件和页面设计
- [后端开发规范](docs/07-后端开发规范.md) - 后端架构和编码规范
- [详细开发步骤](docs/08-详细开发步骤.md) - 68个详细开发步骤

### 专项文档
- [翻译系统设计](docs/09-翻译系统设计.md) - AI翻译工作流程
- [部署配置指南](docs/10-部署配置指南.md) - 生产环境部署方案
- [测试策略](docs/11-测试策略.md) - 完整的测试策略和实施
- [本地开发环境](docs/12-本地开发环境.md) - Mac开发环境配置

## 🔧 开发指南

### 开发流程
1. **需求分析**: 参考项目需求分析文档
2. **架构设计**: 遵循技术架构设计文档
3. **编码实现**: 按照详细开发步骤执行
4. **测试验证**: 执行测试策略中的测试用例
5. **部署上线**: 按照部署配置指南操作

### 代码规范
- **TypeScript**: 严格类型检查，使用最新ES特性
- **代码风格**: 使用Prettier格式化，ESLint检查
- **注释规范**: 关键函数必须有详细中文注释
- **测试覆盖**: 单元测试覆盖率≥80%

### Git工作流
```bash
# 功能开发
git checkout -b feature/article-management
git add .
git commit -m "feat: 实现文章管理功能"
git push origin feature/article-management

# 代码审查后合并
git checkout main
git merge feature/article-management
git push origin main
```

## 🚀 部署指南

### 生产环境
- **服务器**: Debian 12 + 宝塔面板
- **数据库**: MySQL 9.0.1 (************:3306)
- **域名**: 
  - 英语: petcare-usa.com
  - 德语: haustier-deutschland.de
  - 俄语: domashnie-zhivotnye.ru
  - 管理: admin.petcare-usa.com

### 部署步骤
1. 配置服务器环境
2. 设置域名和SSL证书
3. 部署后端API服务
4. 构建并部署前端静态文件
5. 配置Nginx反向代理
6. 设置监控和备份

详细部署步骤请参考 [部署配置指南](docs/10-部署配置指南.md)

## 🧪 测试

### 运行测试
```bash
# 后端测试
cd backend && npm test

# 前端测试
cd frontend && npm test

# E2E测试
npx playwright test

# 测试覆盖率
npm run test:coverage
```

### 测试策略
- **单元测试**: Jest + Vitest
- **集成测试**: Supertest
- **E2E测试**: Playwright
- **性能测试**: K6

## 📊 项目统计

### 开发进度
- **文档完成度**: 100% ✅
- **后端开发**: 0% 🚧
- **前端开发**: 0% 🚧
- **管理后台**: 0% 🚧
- **测试覆盖**: 0% 🚧

### 代码统计
- **预计代码行数**: ~50,000行
- **预计开发时间**: 8-12周
- **开发步骤**: 68个详细步骤

## 🤝 贡献指南

### 开发环境
1. Fork项目到个人仓库
2. 按照本地开发环境文档配置环境
3. 创建功能分支进行开发
4. 提交Pull Request

### 代码审查
- 代码必须通过所有测试
- 遵循项目编码规范
- 包含必要的文档更新
- 通过代码审查

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- **项目负责人**: 开发团队
- **技术支持**: 通过GitHub Issues
- **文档问题**: 参考docs目录下的详细文档

---

## 🎯 下一步计划

根据详细开发步骤文档，接下来的开发重点：

1. **阶段1**: 项目初始化 (步骤1-8)
2. **阶段2**: 数据库设计与实现 (步骤9-16)
3. **阶段3**: 后端API开发 (步骤17-32)
4. **阶段4**: 前端模板开发 (步骤33-48)
5. **阶段5**: 管理后台开发 (步骤49-60)
6. **阶段6**: 集成测试与部署 (步骤61-68)

每个阶段都有详细的任务分解和验证标准，确保开发质量和进度可控。

**开始开发前，请务必仔细阅读所有文档，特别是 [详细开发步骤](docs/08-详细开发步骤.md) 文档。**
